/* Import website animations and modern styling */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* CSS Variables (matching website theme) */
:root {
    --primary-color: #00b4ff;
    --primary-hover: #0099e6;
    --secondary-color: #1a1a1a;
    --background-dark: #0a0a0a;
    --background-card: #111111;
    --background-hover: #1a1a1a;
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --text-muted: #666666;
    --border-color: #333333;
    --success-color: #00ff88;
    --warning-color: #ffaa00;
    --error-color: #ff4444;
    --gradient-primary: linear-gradient(135deg, #00b4ff, #0099e6);
    --gradient-dark: linear-gradient(135deg, #1a1a1a, #0a0a0a);
    --shadow-glow: 0 0 20px rgba(0, 180, 255, 0.3);
    --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
    --border-radius: 12px;
    --transition-fast: 0.2s ease;
    --transition-smooth: 0.3s ease;
    --background-primary: #0a0a0a;
    --animation-duration: 0.3s;
    --bg-primary: #1e293b;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    overflow: hidden;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--background-dark);
    color: var(--text-primary);
    overflow: hidden;
    height: 100vh;
    user-select: none;
    -webkit-font-smoothing: antialiased;
}

/* Background Effects */
.bg-effects {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particles::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(0, 180, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 180, 255, 0.1) 0%, transparent 50%);
    animation: particleFloat 20s infinite linear;
}

.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 180, 255, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 180, 255, 0.05) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 30s infinite linear;
}

@keyframes particleFloat {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

/* App Container */
.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

/* Header */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: var(--background-card);
    border-bottom: 1px solid var(--border-color);
    -webkit-app-region: drag;
    height: 60px;
    position: relative;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    -webkit-app-region: no-drag;
}

.header-center {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    -webkit-app-region: no-drag;
}

.current-tab-indicator {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    background: var(--background-hover);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.menu-toggle {
    width: 40px;
    height: 40px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    transition: var(--transition-smooth);
    -webkit-app-region: no-drag;
}

.menu-toggle:hover {
    background: var(--background-hover);
    border-color: var(--primary-color);
}

.hamburger {
    width: 18px;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: var(--transition-smooth);
}

.menu-toggle.open .hamburger:nth-child(1) {
    transform: translateY(6px) rotate(45deg);
}

.menu-toggle.open .hamburger:nth-child(2) {
    opacity: 0;
}

.menu-toggle.open .hamburger:nth-child(3) {
    transform: translateY(-6px) rotate(-45deg);
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.app-logo {
    width: 28px;
    height: 28px;
}

.app-title {
    font-size: 1.3rem;
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.version-tag {
    background: var(--primary-color);
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 6px;
    font-size: 0.7rem;
    font-weight: 500;
}

.window-controls {
    display: flex;
    gap: 6px;
    -webkit-app-region: no-drag;
}

.control-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: var(--background-hover);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
}

.control-btn:hover {
    background: var(--border-color);
    color: var(--text-primary);
}

.control-btn.close:hover {
    background: var(--error-color);
    color: white;
}

.control-btn.account {
    margin-right: 8px;
    position: relative;
}

.control-btn.account::after {
    content: '';
    position: absolute;
    right: -8px;
    top: 25%;
    height: 50%;
    width: 1px;
    background: var(--border-color);
}

.control-btn.account:hover {
    background: var(--primary-color);
    color: white;
}

/* Main Layout Structure */
.app-header {
    flex-shrink: 0;
}

.content-wrapper {
    flex: 1;
    display: flex;
    flex-direction: row;
    min-height: 0;
    overflow: hidden;
}

.sidebar {
    position: fixed;
    top: 60px; /* Position below header */
    left: 0;
    height: calc(100vh - 60px); /* Full remaining height */
    z-index: 900;
    width: 280px;
    background: var(--background-card);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    transition: transform var(--transition-smooth), opacity var(--transition-smooth);
    display: flex;
    flex-direction: column;
    transform: translateX(-100%);
    opacity: 0;
    pointer-events: none;
}

.sidebar:not(.collapsed) {
    transform: translateX(0);
    opacity: 1;
    pointer-events: auto;
    box-shadow: 0 10px 50px rgba(0,0,0,0.3);
}

.main-content {
    flex: 1;
    background: var(--background-dark);
    padding: 2rem;
    overflow-y: auto;
    transition: margin-left var(--transition-smooth);
}

.nav-items {
    display: flex;
    flex-direction: column;
    gap: 6px;
    flex: 1;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 14px 12px;
    background: transparent;
    border: none;
    border-radius: 10px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    font-size: 0.95rem;
    font-weight: 500;
    text-align: left;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
}

.nav-item:hover {
    background: var(--background-hover);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.nav-item.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-glow);
}

.nav-item .icon {
    font-size: 1.3rem;
    width: 24px;
    text-align: center;
    flex-shrink: 0;
}

.nav-text {
    transition: var(--transition-smooth);
    overflow: hidden;
    white-space: nowrap;
}

.sidebar-footer {
    border-top: 1px solid var(--border-color);
    padding-top: 1rem;
    margin-top: auto;
}

.info-item {
    cursor: default;
    padding: 8px 12px;
    opacity: 0.7;
}

.info-item:hover {
    background: transparent;
    border: none;
    opacity: 1;
}

.info-item .nav-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.info-item small {
    font-size: 0.75rem;
    color: var(--text-muted);
}

#memory-usage, #cpu-usage {
    color: var(--primary-color);
    font-weight: 600;
}

/* Main Content Area */
.main-content {
    flex: 1;
    background: var(--background-dark);
    transition: var(--transition-smooth);
    padding: 2rem;
    overflow-y: auto;
}

.tab-content {
    display: none;
    animation: fadeInUp 0.4s ease-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes gentleGlow {
    0%, 100% {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    }
    50% {
        text-shadow: 0 0 25px rgba(255, 255, 255, 0.5), 0 0 35px rgba(0, 212, 255, 0.3);
    }
}

.content-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.content-header h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.content-header p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

/* Modern Desktop Dashboard Styles */
.modern-dashboard {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.dashboard-section.full-width {
    grid-column: 1 / -1;
}

.dashboard-section {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.8s ease-out;
}

.dashboard-section:nth-child(2) {
    animation-delay: 0.2s;
    animation-fill-mode: both;
}

.dashboard-section:nth-child(3) {
    animation-delay: 0.4s;
    animation-fill-mode: both;
}

.dashboard-section:nth-child(4) {
    animation-delay: 0.6s;
    animation-fill-mode: both;
}

.dashboard-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: var(--transition-smooth);
}

.dashboard-section:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 180, 255, 0.15);
    border-color: rgba(0, 180, 255, 0.3);
}

.dashboard-section:hover::before {
    opacity: 1;
}

/* Remove hover effects from welcome-clock section */
.welcome-clock-section:hover {
    transform: none;
    box-shadow: none;
    border-color: var(--border-color);
}

.welcome-clock-section:hover::before {
    opacity: 0;
}

.launch-section {
    grid-column: 1 / -1;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.add-shortcut-btn, .refresh-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.add-shortcut-btn:hover, .refresh-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

.edit-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--text-primary);
    font-size: 1rem;
}

.edit-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.clock-edit-btn {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition-fast);
    color: var(--text-primary);
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
    z-index: 10;
}

.clock-edit-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 180, 255, 0.3);
}

.refresh-btn {
    padding: 0.5rem;
    font-size: 1rem;
}

/* Quick Launch Grid */
.launch-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
}

.launch-app {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-decoration: none;
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.launch-app:nth-child(1) { animation-delay: 0.1s; }
.launch-app:nth-child(2) { animation-delay: 0.2s; }
.launch-app:nth-child(3) { animation-delay: 0.3s; }
.launch-app:nth-child(4) { animation-delay: 0.4s; }
.launch-app:nth-child(5) { animation-delay: 0.5s; }
.launch-app:nth-child(6) { animation-delay: 0.6s; }
.launch-app:nth-child(n+7) { animation-delay: 0.7s; }

.launch-app::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.launch-app:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 180, 255, 0.3);
}

.launch-app:hover::before {
    left: 100%;
}

.app-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    background: rgba(0, 180, 255, 0.1);
    transition: var(--transition-fast);
}

.launch-app:hover .app-icon {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.app-name {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

.add-app-card {
    border-style: dashed;
    opacity: 0.7;
    justify-content: center;
}

.add-app-card:hover {
    opacity: 1;
    background: rgba(0, 180, 255, 0.1);
    border-color: var(--primary-color);
}

.add-app-icon {
    font-size: 2rem;
    color: var(--text-secondary);
}

/* Compact Welcome & Clock Header */
.welcome-clock-section {
    margin-bottom: 2rem;
    animation: slideInFromTop 0.8s ease-out;
}

.welcome-clock-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--gradient-primary), rgba(0, 180, 255, 0.8));
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 180, 255, 0.2);
    position: relative;
    overflow: hidden;
}



.compact-welcome {
    color: white;
    flex: 1;
}

.welcome-message {
    margin: 0 0 0.5rem 0;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out 0.3s both;
}

.welcome-subtitle {
    margin: 0;
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 400;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.clock-calendar-compact {
    display: flex;
    align-items: center;
    gap: 2rem;
    color: white;
}

.clock-display {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    animation: slideInFromRight 1s ease-out 0.4s both;
}



.digital-clock-compact {
    display: flex;
    align-items: baseline;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
}

.time-compact {
    font-size: 2rem;
    font-weight: 700;
    color: white;
    font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    animation: gentleGlow 3s infinite ease-in-out;
}





















.period-compact {
    font-size: 1rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.8);
    margin-left: 0.25rem;
}

.date-compact {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
}

.day-name-compact {
    font-size: 1rem;
    font-weight: 600;
    color: white;
}

.date-detail-compact {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.8);
}

.mini-calendar-compact {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInFromRight 1s ease-out 0.8s both;
}

.calendar-header-compact {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
    padding: 0 0.25rem;
}

.calendar-month-compact {
    font-size: 0.9rem;
    font-weight: 600;
    color: white;
}

.calendar-nav-compact {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 6px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
    font-size: 1rem;
    font-weight: bold;
}

.calendar-nav-compact:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.calendar-grid-compact {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    font-size: 0.75rem;
}

.calendar-weekday-compact {
    text-align: center;
    font-size: 0.7rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.6);
    padding: 0.25rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 0.25rem;
}

.calendar-day-compact {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    position: relative;
    min-height: 24px;
}

.calendar-day-compact:hover {
    background: rgba(255, 255, 255, 0.2);
}

.calendar-day-compact.today {
    background: rgba(255, 255, 255, 0.3);
    font-weight: 700;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.4);
}

.calendar-day-compact.other-month {
    opacity: 0.4;
}

.calendar-day-compact.other-month:hover {
    opacity: 0.7;
}

/* Quick Access Grid */
.quick-access-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
}

.favorite-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
    padding: 1.5rem 1rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-decoration: none;
    color: var(--text-primary);
    position: relative;
    overflow: hidden;
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.favorite-card:nth-child(1) { animation-delay: 0.1s; }
.favorite-card:nth-child(2) { animation-delay: 0.2s; }
.favorite-card:nth-child(3) { animation-delay: 0.3s; }
.favorite-card:nth-child(4) { animation-delay: 0.4s; }
.favorite-card:nth-child(5) { animation-delay: 0.5s; }
.favorite-card:nth-child(6) { animation-delay: 0.6s; }
.favorite-card:nth-child(n+7) { animation-delay: 0.7s; }

.favorite-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.favorite-card:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 180, 255, 0.3);
}

.favorite-card:hover::before {
    left: 100%;
}

.favorite-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    background: rgba(0, 180, 255, 0.1);
    transition: var(--transition-fast);
}

.favorite-card:hover .favorite-icon {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.favorite-name {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

.add-favorite-card {
    border-style: dashed;
    opacity: 0.7;
    justify-content: center;
}

.add-favorite-card:hover {
    opacity: 1;
    background: rgba(0, 180, 255, 0.1);
    border-color: var(--primary-color);
}

.add-favorite-icon {
    font-size: 2rem;
    color: var(--text-secondary);
}

/* Custom Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(4px);
}

.modal-overlay.active {
    display: flex;
}

.modal-content {
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-medium {
    max-width: 550px;
}

.modal-large {
    max-width: 700px;
}

.modal-xlarge {
    max-width: 1000px;
    width: 95%;
    height: 85vh;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
}

.modal-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.modal-header p {
    margin: 0.5rem 0 0 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: var(--transition-fast);
    position: absolute;
    top: 1rem;
    right: 1rem;
}

.modal-close:hover {
    background: var(--background-hover);
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

/* Quick Access Modal Styles */
.modal-section {
    margin-bottom: 2rem;
}

.modal-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.quick-access-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 0.75rem;
}

.option-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.option-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 180, 255, 0.1), transparent);
    transition: var(--transition-smooth);
}

.option-card:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 180, 255, 0.3);
}

.option-card:hover::before {
    left: 100%;
}

.option-icon {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 180, 255, 0.1);
    border-radius: 10px;
    flex-shrink: 0;
    transition: var(--transition-fast);
}

.option-card:hover .option-icon {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.option-info {
    flex: 1;
    min-width: 0;
}

.option-name {
    display: block;
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
}

.option-desc {
    display: block;
    font-size: 0.8rem;
    opacity: 0.8;
    line-height: 1.3;
}

.browse-section {
    text-align: center;
    padding: 1rem 0;
}

/* Clock Settings Modal Styles */
.settings-section {
    margin-bottom: 2rem;
}

.settings-section h4 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.setting-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Ensure clock settings modal content is visible */
#clock-settings-modal .modal-body {
    display: block !important;
}

#clock-settings-modal .settings-section {
    display: block !important;
}

#clock-settings-modal .setting-group {
    display: flex !important;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: var(--background-hover);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.setting-item label {
    font-weight: 500;
    color: var(--text-primary);
}

/* Browser Settings Styles */
.setting-select {
    padding: 0.5rem 0.75rem;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.setting-select:hover {
    border-color: var(--accent-color);
}

.setting-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.1);
}

.setting-input {
    padding: 0.5rem 0.75rem;
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: all 0.3s ease;
    width: 100%;
}

.setting-input:hover {
    border-color: var(--accent-color);
}

.setting-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(0, 212, 255, 0.1);
}

.custom-url-input {
    margin-top: 0.75rem;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from { 
        opacity: 0; 
        max-height: 0; 
        margin-top: 0;
    }
    to { 
        opacity: 1; 
        max-height: 100px;
        margin-top: 0.75rem;
    }
}

.setting-item select {
    background: var(--background-primary);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.5rem;
    color: var(--text-primary);
    font-size: 0.9rem;
    min-width: 150px;
}

.setting-item select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

.toggle-setting {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
}

.toggle-setting span {
    font-weight: 500;
    color: var(--text-primary);
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    gap: 1rem;
}

/* Toggle Switch Styles */
.toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--background-secondary);
    border: 1px solid var(--border-color);
    transition: var(--transition-fast);
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background-color: var(--text-secondary);
    transition: var(--transition-fast);
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
    background-color: white;
}

.toggle:hover .slider {
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

.btn-browse {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.btn-browse:hover {
    background: rgba(0, 180, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 180, 255, 0.4);
}

/* File Browser Styles */
.file-browser-container {
    display: flex;
    flex-direction: column;
    height: 70vh;
}

.breadcrumb {
    margin: 0.5rem 0 0 0;
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.breadcrumb-item {
    color: var(--primary-color);
}

.file-nav {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 0;
    border-bottom: 1px solid var(--border-color);
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    cursor: pointer;
    font-size: 0.85rem;
    transition: var(--transition-fast);
}

.nav-btn:hover:not(:disabled) {
    background: var(--primary-color);
    color: white;
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.path-input {
    flex: 1;
}

.path-input input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 0.85rem;
}

.file-browser-layout {
    display: flex;
    flex: 1;
    min-height: 0;
}

.file-sidebar {
    width: 220px;
    background: var(--background-hover);
    border-right: 1px solid var(--border-color);
    padding: 1rem;
    overflow-y: auto;
    flex-shrink: 0;
}

.sidebar-section {
    margin-bottom: 1.5rem;
}

.sidebar-section h5 {
    margin: 0 0 0.75rem 0;
    color: var(--text-primary);
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.sidebar-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.85rem;
    color: var(--text-primary);
    transition: var(--transition-fast);
    margin-bottom: 0.25rem;
}

.sidebar-item:hover {
    background: rgba(0, 180, 255, 0.1);
    color: var(--primary-color);
}

.sidebar-icon {
    font-size: 1rem;
    width: 16px;
    text-align: center;
}

.file-main {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.file-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
}

.file-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 0.75rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-align: center;
}

.file-item:hover {
    background: rgba(0, 180, 255, 0.1);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

.file-item.selected {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.file-item-icon {
    font-size: 2rem;
    margin-bottom: 0.25rem;
}

.file-item-name {
    font-size: 0.8rem;
    font-weight: 500;
    word-break: break-word;
    line-height: 1.2;
}

.file-browser-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0 0 0;
    border-top: 1px solid var(--border-color);
    margin-top: 1rem;
}

.selected-file {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.file-actions {
    display: flex;
    gap: 0.75rem;
}

.btn-cancel, .btn-confirm {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.btn-cancel {
    background: var(--background-hover);
    color: var(--text-secondary);
}

.btn-cancel:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.btn-confirm {
    background: var(--primary-color);
    color: white;
}

.btn-confirm:hover:not(:disabled) {
    background: rgba(0, 180, 255, 0.8);
    transform: translateY(-1px);
}

.btn-confirm:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Loading and Empty States */
.loading-recent {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 3rem 1rem;
}

.empty-apps {
    grid-column: 1 / -1;
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 3rem 1rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .modern-dashboard {
        grid-template-columns: 1fr;
    }
    
    .quick-access-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}

@media (max-width: 768px) {
    .modern-dashboard {
        gap: 1.5rem;
    }
    
    .dashboard-section {
        padding: 1.5rem;
    }
    
    .welcome-clock-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
        padding: 1.25rem;
    }

    .clock-calendar-compact {
        flex-direction: column;
        gap: 1rem;
    }

    .clock-display {
        text-align: center;
    }

    .mini-calendar-compact {
        width: 100%;
        max-width: 280px;
        margin: 0 auto;
    }

    .welcome-message {
        font-size: 1.4rem;
    }

    .time-compact {
        font-size: 1.6rem;
    }
    
    .launch-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.75rem;
    }
    
    .launch-app {
        padding: 1rem 0.75rem;
    }
    
    .app-icon {
        width: 40px;
        height: 40px;
        font-size: 1.6rem;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .section-header h3 {
        font-size: 1.2rem;
    }
}

/* Scrollbar Styles */
.recent-files-container::-webkit-scrollbar {
    width: 6px;
}

.recent-files-container::-webkit-scrollbar-track {
    background: var(--background-hover);
    border-radius: 3px;
}

.recent-files-container::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 3px;
}

.recent-files-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

.action-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-card);
    display: flex;
    flex-direction: column;
    gap: 1rem;
    transition: var(--transition-smooth);
}

.action-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 180, 255, 0.2);
}

.action-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.action-card p {
    color: var(--text-secondary);
    margin: 0;
    flex: 1;
}

.recent-activity {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-card);
}

.recent-activity h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--background-hover);
    border-radius: 8px;
    transition: var(--transition-fast);
}

.activity-item:hover {
    background: var(--border-color);
}

.activity-icon {
    font-size: 1.5rem;
    width: 40px;
    text-align: center;
}

.activity-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
}

.activity-title {
    font-weight: 600;
    color: var(--text-primary);
}

.activity-time {
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Cleanup Tab Styles */
.cleanup-container {
    display: block;
}

.status-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-card);
}

.status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.status-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
}

.status-indicator {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    background: var(--success-color);
    color: var(--background-dark);
}

.progress-section {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--background-hover);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 4px;
    transition: width var(--transition-smooth);
    width: 0%;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Options Grid */
.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
}

.option-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: var(--transition-smooth);
}

.option-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 20px rgba(0, 180, 255, 0.2);
    transform: translateY(-2px);
}

.option-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.option-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
}

/* Toggle Switch */
.toggle {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 24px;
    flex-shrink: 0; /* Prevent shrinking */
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #333;
    transition: 0.3s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--primary-color);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

/* Ensure settings toggles are consistent */
.settings-section .toggle,
.widget-settings-panel .toggle {
    width: 44px !important;
    height: 24px !important;
    min-width: 44px;
    max-width: 44px;
}

.settings-section .slider,
.widget-settings-panel .slider {
    width: 44px !important;
    height: 24px !important;
}

.settings-section .slider:before,
.widget-settings-panel .slider:before {
    height: 18px !important;
    width: 18px !important;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-smooth);
    min-width: 150px;
    justify-content: center;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 180, 255, 0.4);
}

.btn-secondary {
    background: var(--background-card);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #555, #444);
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 1.1rem;
}

.btn-warning {
    background: linear-gradient(135deg, #ff9800, #f57c00);
    color: #fff;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #f57c00, #e65100);
    transform: translateY(-2px);
}

/* Game Grid */
.game-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
    margin-bottom: 2rem;
}

.game-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.game-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 180, 255, 0.2);
}

.game-card img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.game-card .game-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.8;
    display: none;
}

.game-card img[style*="display: none"] + .game-icon,
.game-card:not(:has(img)) .game-icon {
    display: block;
}

.game-card h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.game-card p {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0;
}

/* Mod List Enhancements */
.mod-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    margin-top: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.mod-header h3 {
    margin: 0;
    color: var(--primary-color);
}

.mod-header-actions {
    display: flex;
    gap: 0.5rem;
}

.mod-category-header {
    margin: 1.5rem 0 1rem 0;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.mod-category-header h4 {
    margin: 0;
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
}

.mod-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 0.5rem;
    transition: var(--transition-smooth);
}

.mod-item:hover {
    border-color: var(--primary-color);
    transform: translateX(4px);
    box-shadow: 0 4px 15px rgba(0, 180, 255, 0.1);
}

.mod-info {
    flex: 1;
    min-width: 0;
}

.mod-info h4 {
    margin: 0 0 0.25rem 0;
    font-weight: 600;
    color: var(--text-primary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.mod-info p {
    margin: 0 0 0.25rem 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.mod-path {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-family: monospace;
    word-break: break-all;
    display: block;
    margin-top: 0.25rem;
}

.mod-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
    margin-left: 1rem;
}

.mod-list-content {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

/* Enhanced placeholder styling */
.placeholder {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 2rem;
    background: var(--background-card);
    border: 1px dashed var(--border-color);
    border-radius: var(--border-radius);
    margin: 1rem 0;
}

/* Tools Grid */
.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.tool-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    cursor: pointer;
    transition: var(--transition-smooth);
    text-align: center;
    position: relative;
}

.tool-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 180, 255, 0.2);
}

.tool-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.tool-card h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tool-card p {
    color: var(--text-secondary);
}

.coming-soon {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--warning-color);
    color: var(--background-dark);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 600;
}

/* System Tools Interface */
.tools-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Main Tools Grid Layout */
.main-tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.tool-panel {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--shadow-card);
}

.tool-panel.compact {
    padding: 1rem;
}

.tool-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.tool-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.tool-status {
    padding: 0.4rem 0.8rem;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tool-status.active {
    background: var(--success-color);
    color: var(--background-dark);
    box-shadow: 0 0 8px rgba(0, 255, 136, 0.3);
}

.tool-status.inactive {
    background: var(--border-color);
    color: var(--text-secondary);
}

.tool-config {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.tool-config.compact {
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.config-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.config-row {
    display: flex;
    flex-direction: column;
    gap: 0.4rem;
}

.config-row.inline {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 0.5rem;
}

.config-row label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9rem;
    white-space: nowrap;
}

.config-row select,
.config-row input[type="text"] {
    padding: 0.5rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: var(--transition-fast);
    min-width: 0;
    flex: 1;
}

.config-row.inline select {
    max-width: 80px;
    flex: 0 0 auto;
}

.config-row select:focus,
.config-row input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

.config-row input[type="range"] {
    width: 100%;
    height: 4px;
    background: var(--background-hover);
    border-radius: 2px;
    outline: none;
    -webkit-appearance: none;
    margin-top: 0.25rem;
}

.config-row input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 180, 255, 0.3);
    transition: var(--transition-fast);
}

.config-row input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.1);
}

.config-row input[type="range"]::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 180, 255, 0.3);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    position: relative;
    font-size: 0.9rem;
}

.checkbox-label.compact {
    padding-left: 1.5rem;
}

.checkbox-label input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkmark {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    height: 14px;
    width: 14px;
    background: var(--background-hover);
    border: 2px solid var(--border-color);
    border-radius: 3px;
    transition: var(--transition-fast);
}

.checkbox-label:hover .checkmark {
    border-color: var(--primary-color);
}

.checkbox-label input:checked ~ .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 3px;
    top: 1px;
    width: 3px;
    height: 6px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.checkbox-label input:checked ~ .checkmark:after {
    display: block;
}

/* Help Button Styles */
.help-button {
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-left: 8px;
    transition: var(--transition-smooth);
    color: var(--text-secondary);
}

.help-button:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

.help-icon {
    font-size: 12px;
    font-weight: bold;
}

.tool-description {
    background: var(--background-hover);
    padding: 0.75rem;
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
    margin-top: 0.5rem;
}

.tool-description.compact {
    padding: 0.5rem 0.75rem;
    margin-top: 0.25rem;
}

.tool-description p {
    margin: 0;
    color: var(--text-secondary);
    font-style: italic;
    font-size: 0.85rem;
}

.tool-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.tool-actions .btn {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0.75rem 0.5rem;
    font-size: 0.85rem;
}

/* Additional Tools Section */
.additional-tools {
    margin-top: 1rem;
}

.additional-tools h3 {
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.tools-grid.compact {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-top: 0;
}

.tool-card.compact {
    padding: 1rem;
    text-align: center;
    position: relative;
}

.tool-card.compact .tool-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.tool-card.compact h4 {
    font-size: 1rem;
    margin-bottom: 0;
}

.tool-card.compact p {
    display: none; /* Hide descriptions in compact mode */
}

/* Responsive design for compact layout */
@media (max-width: 1200px) {
    .main-tools-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-tools-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .config-group {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .config-row.inline {
        flex-direction: column;
        align-items: stretch;
        gap: 0.25rem;
    }
    
    .config-row.inline select {
        max-width: none;
    }
    
    .checkbox-label.compact {
        padding-left: 0;
        justify-content: flex-start;
    }
    
    .checkmark {
        position: relative;
        left: auto;
        top: auto;
        transform: none;
        margin-right: 0.5rem;
    }
    
    .tools-grid.compact {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Settings */
.settings-container {
    display: flex;
    gap: 2rem;
    height: calc(100vh - 200px);
    min-height: 600px;
}

.settings-nav {
    flex: 0 0 200px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 1rem;
    height: fit-content;
}

.settings-nav-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background: transparent;
    border: none;
    color: #ccc;
    text-align: left;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.settings-nav-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

.settings-nav-btn.active {
    background: var(--primary-color);
    color: #fff;
}

.settings-nav-btn .nav-icon {
    font-size: 1.1rem;
}

.settings-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 2rem;
    overflow-y: auto;
}

.settings-section {
    display: none;
}

.settings-section.active {
    display: block;
}

.settings-section h3 {
    color: #fff;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.5rem;
}

.setting-group {
    margin-bottom: 2.5rem;
}

.setting-group h4 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    color: #fff;
    font-size: 0.95rem;
    flex: 1;
}

.setting-item select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    color: #fff;
    padding: 0.5rem 1rem;
    min-width: 120px;
}

.setting-item select option {
    background: #1a1a1a;
    color: #fff;
}

/* Theme Presets */
.theme-presets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.theme-preset {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.theme-preset:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.theme-preset.active {
    border-color: var(--primary-color);
    background: rgba(0, 180, 255, 0.1);
}

.preset-preview {
    width: 60px;
    height: 40px;
    border-radius: 8px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.theme-preset span {
    color: #fff;
    font-size: 0.85rem;
    text-align: center;
}

/* Color Settings */
.color-settings {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.color-setting {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.color-setting label {
    color: #fff;
    font-size: 0.9rem;
    font-weight: 500;
}

.color-setting input[type="color"] {
    width: 100%;
    height: 50px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    background: none;
}

.color-setting input[type="color"]::-webkit-color-swatch-wrapper {
    padding: 0;
}

.color-setting input[type="color"]::-webkit-color-swatch {
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
}

/* Settings Actions */
.settings-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-actions .btn {
    min-width: 150px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .settings-container {
        flex-direction: column;
        height: auto;
    }
    
    .settings-nav {
        flex: none;
        display: flex;
        overflow-x: auto;
        padding: 1rem;
        gap: 0.5rem;
    }
    
    .settings-nav-btn {
        flex: 0 0 auto;
        white-space: nowrap;
        margin-bottom: 0;
    }
    
    .settings-content {
        padding: 1.5rem;
    }
    
    .theme-presets {
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    }
    
    .color-settings {
        grid-template-columns: 1fr;
    }
    
    .settings-actions {
        flex-direction: column;
    }
    
    .settings-actions .btn {
        width: 100%;
    }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.loading-overlay.hidden {
    display: none;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    font-size: 1.1rem;
    color: var(--text-primary);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--background-card);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-header {
        padding: 0.5rem 1rem;
    }
    
    .header-left {
        gap: 12px;
    }
    
    .current-tab-indicator {
        font-size: 1rem;
        padding: 0.4rem 0.8rem;
    }
    
    .sidebar {
        width: 280px;
        position: fixed;
        left: 0;
        top: 60px;
        height: calc(100vh - 60px);
        z-index: 999;
        transform: translateX(-100%);
        transition: transform var(--transition-smooth);
    }
    
    .sidebar:not(.collapsed) {
        transform: translateX(0);
    }
    
    .sidebar.collapsed {
        transform: translateX(-100%);
        width: 280px;
    }
    
    .main-content {
        padding: 1rem;
        margin-left: 0;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .options-grid, .tools-grid, .game-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        align-items: stretch;
    }
    
    .content-header h2 {
        font-size: 1.5rem;
    }
    
    .logo-section {
        gap: 8px;
    }
    
    .app-title {
        font-size: 1.1rem;
    }
    
    .version-tag {
        display: none;
    }
}

/* Mod Tabs Interface */
.mod-tabs-container {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.mod-tabs {
    display: flex;
    background: var(--background-hover);
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
}

.mod-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-smooth);
    white-space: nowrap;
    position: relative;
    font-size: 0.95rem;
    font-weight: 500;
}

.mod-tab:hover {
    background: var(--background-card);
    color: var(--text-primary);
}

.mod-tab.active {
    background: var(--background-card);
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.mod-tab.active::before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    animation: tabSlideIn 0.3s ease;
}

@keyframes tabSlideIn {
    from {
        transform: scaleX(0);
    }
    to {
        transform: scaleX(1);
    }
}

.tab-icon {
    font-size: 1.1rem;
}

.tab-label {
    font-weight: 600;
}

.tab-count {
    background: var(--border-color);
    color: var(--text-primary);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
    min-width: 1.5rem;
    text-align: center;
}

.mod-tab.active .tab-count {
    background: var(--primary-color);
    color: white;
}

.mod-tab-contents {
    position: relative;
    min-height: 300px;
}

.mod-tab-content {
    display: none;
    padding: 1.5rem;
    animation: fadeInUp 0.3s ease;
}

.mod-tab-content.active {
    display: block;
}

.mod-category-info {
    margin-bottom: 1rem;
    padding: 0.75rem 1rem;
    background: var(--background-hover);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
}

.mod-category-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-style: italic;
}

/* Remove old category header styles since we're using tabs now */
.mod-category-header {
    display: none;
}

/* Responsive design for tabs */
@media (max-width: 768px) {
    .mod-tabs {
        flex-direction: column;
    }
    
    .mod-tab {
        justify-content: space-between;
        padding: 0.75rem 1rem;
    }
    
    .mod-tab.active::before {
        display: none;
    }
    
    .mod-tab.active {
        border-bottom: none;
        border-left: 3px solid var(--primary-color);
    }
    
    .mod-tab-content {
        padding: 1rem;
    }
    
    .mod-category-info {
        padding: 0.5rem 0.75rem;
    }
}

/* Reduce motion support */
.reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}

/* Modal Overlay */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
    animation: fadeIn var(--animation-duration) ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* License Management Styles */
.license-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* License Status Card */
.license-status-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 2rem;
    backdrop-filter: blur(10px);
}

.license-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.license-icon {
    font-size: 3rem;
    opacity: 0.8;
}

.license-info h3 {
    margin: 0 0 0.5rem 0;
    color: #fff;
    font-size: 1.5rem;
}

.license-info p {
    margin: 0;
    color: #ccc;
    font-size: 0.9rem;
}

.license-badge {
    margin-left: auto;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    background: #333;
    color: #fff;
}

.license-badge.active {
    background: var(--primary-color);
    color: #fff;
}

.license-badge.expired {
    background: #ff4444;
    color: #fff;
}

.license-badge.premium {
    background: linear-gradient(135deg, #00d4ff, #00ff88);
    color: #000;
    text-shadow: none;
}

.license-badge.trial {
    background: linear-gradient(135deg, #ff9500, #ffb800);
    color: #000;
    text-shadow: none;
}

.license-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-label {
    color: #ccc;
    font-weight: 500;
}

.detail-value {
    color: #fff;
    font-family: monospace;
    font-size: 0.9rem;
}

/* License Activation */
.license-activation {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 2rem;
}

.license-activation h3 {
    color: #fff;
    margin-bottom: 1.5rem;
}

.activation-form .input-group {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.activation-form input {
    flex: 1;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #fff;
    font-family: monospace;
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.activation-form input::placeholder {
    color: #888;
}

.activation-hint {
    color: #ccc;
    font-size: 0.9rem;
    margin: 0;
}

/* License Plans */
.license-plans h3 {
    color: #fff;
    margin-bottom: 2rem;
    text-align: center;
}

.plans-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.plan-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    position: relative;
    transition: all 0.3s ease;
}

.plan-card:hover {
    transform: translateY(-5px);
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 180, 255, 0.2);
}

.plan-card.popular {
    border-color: var(--primary-color);
    background: rgba(0, 180, 255, 0.1);
}

.plan-card.popular::before {
    content: "POPULAR";
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: #fff;
    padding: 0.25rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.plan-card.premium {
    border-color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
}

.plan-header h4 {
    margin: 0 0 1rem 0;
    color: #fff;
    font-size: 1.3rem;
}

.plan-price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.plan-card.premium .plan-price {
    color: #ffd700;
}

.plan-duration {
    color: #ccc;
    font-size: 0.9rem;
    margin-bottom: 2rem;
}

.plan-features {
    text-align: left;
    margin-bottom: 2rem;
}

.feature-item {
    padding: 0.5rem 0;
    color: #ccc;
    font-size: 0.9rem;
}

.feature-item:first-child {
    padding-top: 0;
}

.feature-item:last-child {
    padding-bottom: 0;
}

.btn-premium {
    background: linear-gradient(135deg, #ffd700, #ffb300);
    color: #000;
    font-weight: 600;
}

.btn-premium:hover {
    background: linear-gradient(135deg, #ffb300, #ff9800);
    transform: translateY(-2px);
}

/* Feature Comparison */
.feature-comparison h3 {
    color: #fff;
    margin-bottom: 2rem;
    text-align: center;
}

.comparison-table {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    overflow: hidden;
}

.comparison-header,
.comparison-row {
    display: grid;
    grid-template-columns: 2fr repeat(5, 1fr);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.comparison-header {
    background: rgba(255, 255, 255, 0.1);
    font-weight: 600;
}

.comparison-row:last-child {
    border-bottom: none;
}

.feature-col,
.plan-col {
    padding: 1rem;
    text-align: center;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-col {
    text-align: left;
    color: #fff;
    font-weight: 500;
}

.plan-col {
    color: #ccc;
    font-size: 1.2rem;
}

.feature-col:last-child,
.plan-col:last-child {
    border-right: none;
}

/* License Actions */
.license-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-danger {
    background: linear-gradient(135deg, #ff4444, #cc0000);
    color: #fff;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #cc0000, #b91c1c);
}

/* Responsive Design for License */
@media (max-width: 768px) {
    .license-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .license-badge {
        margin-left: 0;
        align-self: flex-start;
    }
    
    .activation-form .input-group {
        flex-direction: column;
    }
    
    .plans-grid {
        grid-template-columns: 1fr;
    }
    
    .comparison-header,
    .comparison-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .feature-col,
    .plan-col {
        padding: 0.75rem;
    }
    
    .license-actions {
        flex-direction: column;
    }
}

/* Confirmation Modal */
.confirm-modal {
    background: linear-gradient(135deg, #1a1a1a, #2a2a2a);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    max-width: 450px;
    width: 90%;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    animation: slideIn var(--animation-duration) ease;
    overflow: hidden;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 1.5rem 2rem 1rem 2rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-body {
    padding: 1.5rem 2rem;
}

.modal-body p {
    margin: 0;
    color: #ccc;
    line-height: 1.6;
    font-size: 0.95rem;
}

.modal-actions {
    padding: 1rem 2rem 1.5rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.modal-actions .btn {
    min-width: 100px;
}

/* === DESKTOP WIDGET STYLES === */

/* Premium Badge */
.premium-badge {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    font-size: 0.65rem;
    font-weight: 700;
    padding: 0.15rem 0.4rem;
    border-radius: 6px;
    margin-left: 0.5rem;
    display: inline-block;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Desktop Arsenal Card Styling */
.desktop-arsenal-card {
    position: relative;
    cursor: pointer;
    transition: all var(--transition-smooth);
    border: 2px solid transparent;
}

.desktop-arsenal-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 180, 255, 0.2);
}

.desktop-arsenal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 180, 255, 0.05), transparent);
    border-radius: var(--border-radius);
    opacity: 0;
    transition: opacity var(--transition-smooth);
}

.desktop-arsenal-card:hover::before {
    opacity: 1;
}

.desktop-arsenal-card .option-header {
    position: relative;
    z-index: 2;
}

.desktop-arsenal-card .premium-badge {
    position: relative;
    top: 0;
    right: 0;
    margin-left: auto;
    animation: shimmerGold 2s ease-in-out infinite;
}

@keyframes shimmerGold {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
    }
    50% {
        box-shadow: 0 4px 16px rgba(255, 215, 0, 0.6);
    }
}

.arsenal-stats {
    position: relative;
    z-index: 2;
    transition: color var(--transition-smooth);
}

.desktop-arsenal-card:hover .arsenal-stats {
    color: var(--primary-color) !important;
}

/* Widget Container */
.widget-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* Widget Status */
.widget-info {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-top: 1rem;
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
    flex: 1;
    min-width: 150px;
}

.info-label {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.info-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* Widget Actions */
.widget-actions {
    margin-top: 1rem;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 180, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.action-card:hover::before {
    left: 100%;
}

.action-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 180, 255, 0.2);
}

.card-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: block;
}

.action-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.action-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.action-card .btn {
    min-width: 140px;
}

/* Desktop Files List */
.recent-files {
    margin-top: 2rem;
}

.recent-files h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.files-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    max-height: 300px;
    overflow-y: auto;
    padding: 1rem;
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
}

.file-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.file-item:hover {
    border-color: var(--primary-color);
    background: rgba(0, 180, 255, 0.05);
    transform: translateX(4px);
}

.file-icon {
    font-size: 1.8rem;
    flex-shrink: 0;
    width: 40px;
    text-align: center;
}

.file-info {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-size: 0.95rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-details {
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.file-action {
    flex-shrink: 0;
}

.btn-small {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    min-width: auto;
}

/* Widget Settings Panel */
.widget-settings-panel {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-top: 2rem;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.widget-settings-panel h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.5rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.setting-group h4 {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-item label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.setting-item input[type="number"] {
    background: var(--background-hover);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.9rem;
    padding: 0.5rem;
    width: 80px;
    text-align: center;
}

.setting-item input[type="number"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 180, 255, 0.2);
}

.settings-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

/* Status Indicator Variants */
.status-indicator.active {
    background: var(--success-color);
    color: white;
}

.status-indicator.inactive {
    background: var(--text-muted);
    color: var(--text-primary);
}

/* Widget Navigation Enhancement */
.nav-item .premium-badge {
    font-size: 0.6rem;
    padding: 0.1rem 0.3rem;
    margin-left: auto;
}

/* Desktop Widget Disabled State */
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.nav-item:has(.premium-badge) {
    position: relative;
}

/* Responsive Design for Widget */
@media (max-width: 768px) {
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .settings-grid {
        grid-template-columns: 1fr;
    }
    
    .widget-info {
        flex-direction: column;
    }
    
    .files-list {
        max-height: 200px;
    }
    
    .file-item {
        padding: 0.75rem;
    }
    
    .settings-actions {
        flex-direction: column;
    }
    
    .settings-actions .btn {
        width: 100%;
    }
}

/* Tauri drag region for header */
.app-header {
  -webkit-app-region: drag;
}

.window-controls .control-btn {
  -webkit-app-region: no-drag;
}

/* Modal Layout Prevention - Minimal interference approach */
body.modal-open {
    overflow: hidden !important;
    /* Don't use position: fixed to prevent layout recalculations */
}

/* Beta warning banner now works properly with all modals */

/* Professional Custom Modal System */
.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(15px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    /* Prevent interaction with background */
    pointer-events: none;
    /* Prevent any scrolling within the overlay */
    overflow: hidden;
}

.custom-modal-overlay.active {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

.custom-modal-container {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    transform: scale(0.8) translateY(-50px);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* HWID Modal Override - Higher specificity */
.custom-modal-overlay .custom-modal-container.hwid-info-modal {
    max-width: 95vw !important;
    width: 95vw !important;
    min-width: 1200px !important;
    max-height: 90vh !important;
    overflow: hidden;
}

.custom-modal-container.active {
    transform: scale(1) translateY(0);
    opacity: 1;
}

.modal-close-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin-left: auto;
}

.modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: scale(1.1);
}

.custom-modal-header {
    padding: 2rem;
    background: linear-gradient(135deg, #2d3748, #1a202c);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.custom-modal-header.info {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.custom-modal-header.warning {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.custom-modal-header.error {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.custom-modal-header.success {
    background: linear-gradient(135deg, #10b981, #059669);
}

.modal-icon {
    font-size: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.modal-title {
    color: white;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.custom-modal-body {
    padding: 2rem;
    max-height: 50vh;
    overflow-y: auto;
    color: #e2e8f0;
    line-height: 1.6;
}

.custom-modal-body p {
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
}

.custom-modal-body .confirm-message {
    font-size: 1.2rem;
    color: #f7fafc;
    text-align: center;
    margin: 1rem 0;
}

.custom-modal-footer {
    padding: 2rem;
    background: linear-gradient(135deg, #1e293b, #0f172a);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Directory Information Styling */
.directory-info-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.directory-info-item:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
}

.directory-info-item h4 {
    color: var(--primary-color);
    margin: 0 0 1rem 0;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.directory-details {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    color: #94a3b8;
    font-weight: 500;
}

.detail-value {
    color: #f1f5f9;
    font-weight: 600;
    font-size: 0.95rem;
}

.detail-value.path-value {
    color: var(--accent-color);
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    direction: rtl;
    text-align: left;
}

/* License Activation Form Styling */
.license-activation-form {
    text-align: left;
}

.license-activation-form p {
    color: #cbd5e1;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.modal-input {
    width: 100%;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    color: #f1f5f9;
    font-size: 1rem;
    font-family: 'Courier New', monospace;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.modal-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    background: rgba(255, 255, 255, 0.08);
}

.modal-input::placeholder {
    color: #64748b;
}

/* Select dropdown styling for dark theme */
select.modal-input,
.modal-input select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-color: #1f2937 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236366f1' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.8rem center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 2.5rem;
}

select.modal-input option,
.modal-input select option {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 0.5rem;
}

/* Override any browser default select styling */
select {
    background-color: #1f2937;
    color: #f9fafb;
}

select option {
    background-color: #1f2937;
    color: #f9fafb;
}

.license-features {
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.2);
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1.5rem;
}

.license-features h4 {
    color: #10b981;
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.license-features ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.license-features li {
    padding: 0.5rem 0;
    color: #d1fae5;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.license-features li:first-child {
    padding-top: 0;
}

.license-features li:last-child {
    padding-bottom: 0;
}

/* Modal Button Styling */
.custom-modal-footer .btn {
    min-width: 120px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.custom-modal-footer .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.custom-modal-footer .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #2563eb);
    border: none;
    color: white;
}

.custom-modal-footer .btn-primary:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
}

.custom-modal-footer .btn-secondary {
    background: linear-gradient(135deg, #374151, #1f2937);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #d1d5db;
}

.custom-modal-footer .btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563, #374151);
    border-color: rgba(255, 255, 255, 0.2);
    color: #f9fafb;
}

.custom-modal-footer .btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: none;
    color: white;
}

.custom-modal-footer .btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

/* Responsive Design for Custom Modals */
@media (max-width: 768px) {
    .custom-modal-container {
        width: 95%;
        margin: 1rem;
    }
    
    .custom-modal-header,
    .custom-modal-body,
    .custom-modal-footer {
        padding: 1.5rem;
    }
    
    .modal-title {
        font-size: 1.25rem;
    }
    
    .custom-modal-footer {
        flex-direction: column;
    }
    
    .custom-modal-footer .btn {
        width: 100%;
        margin: 0;
    }
    
    .detail-value.path-value {
        max-width: 200px;
    }
}

/* Smooth scrollbar for modal body */
.custom-modal-body::-webkit-scrollbar {
    width: 8px;
}

.custom-modal-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.custom-modal-body::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

.custom-modal-body::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Professional Help Dialog Styling */
.help-content {
    display: grid;
    gap: 2rem;
}

.help-section {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.help-section:hover {
    background: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
}

.help-section h4 {
    color: var(--primary-color);
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    padding-bottom: 0.5rem;
}

.shortcut-list {
    display: grid;
    gap: 0.8rem;
}

.shortcut-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.shortcut-item:hover {
    background: rgba(0, 0, 0, 0.3);
    transform: translateX(5px);
}

.shortcut-key {
    background: linear-gradient(135deg, #374151, #1f2937);
    color: #f1f5f9;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-weight: 600;
    font-size: 0.85rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    min-width: 80px;
    text-align: center;
}

.shortcut-desc {
    color: #cbd5e1;
    font-size: 0.95rem;
    flex: 1;
    margin-left: 1rem;
}

/* Professional Debug Panel Styling */
.debug-panel {
    display: grid;
    gap: 2rem;
}

.debug-section {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.debug-section:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.12);
}

.debug-section h4 {
    color: var(--accent-color);
    margin: 0 0 1.2rem 0;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 1px solid rgba(6, 182, 212, 0.2);
    padding-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.debug-info-grid {
    display: grid;
    gap: 0.8rem;
}

.debug-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border-left: 3px solid var(--primary-color);
    transition: all 0.3s ease;
}

.debug-info-item:hover {
    background: rgba(0, 0, 0, 0.3);
    border-left-color: var(--accent-color);
    transform: translateX(3px);
}

.debug-label {
    color: #94a3b8;
    font-weight: 500;
    font-size: 0.95rem;
}

.debug-value {
    color: #f1f5f9;
    font-weight: 600;
    font-size: 0.95rem;
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.05);
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.debug-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.debug-action-btn {
    padding: 1rem !important;
    font-weight: 600 !important;
    border-radius: 10px !important;
    transition: all 0.3s ease !important;
    text-align: center;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.debug-action-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.debug-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn-tertiary {
    background: linear-gradient(135deg, #64748b, #475569);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #f1f5f9;
}

.btn-tertiary:hover:not(:disabled) {
    background: linear-gradient(135deg, #475569, #334155);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Responsive Design for Help and Debug */
@media (max-width: 768px) {
    .help-content,
    .debug-panel {
        gap: 1.5rem;
    }
    
    .help-section,
    .debug-section {
        padding: 1rem;
    }
    
    .shortcut-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .shortcut-desc {
        margin-left: 0;
    }
    
    .shortcut-key {
        min-width: auto;
        align-self: flex-start;
    }
    
    .debug-actions {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }
    
    .debug-action-btn {
        min-height: 50px;
        padding: 0.8rem !important;
    }
    
    .debug-info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .debug-value {
        align-self: stretch;
        text-align: center;
    }
}

/* Animation for debug info updates */
@keyframes debugValueUpdate {
    0% { background: rgba(16, 185, 129, 0.3); }
    100% { background: rgba(255, 255, 255, 0.05); }
}

.debug-value.updated {
    animation: debugValueUpdate 1s ease;
}

/* Professional Mod Manager Styling */

/* Main Header Section */
.mod-header-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    background: linear-gradient(135deg, #1e293b, #0f172a);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.mod-header-info h3 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.mod-count {
    color: #94a3b8;
    font-size: 1rem;
    font-weight: 500;
    margin-top: 0.5rem;
    display: block;
}

.mod-header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

/* Professional Button Styling */
.btn-professional {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    text-decoration: none;
    min-height: 44px;
    justify-content: center;
}

.btn-professional:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.btn-professional:active {
    transform: translateY(0);
}

.btn-professional.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), #2563eb);
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-professional.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.btn-professional.btn-secondary {
    background: linear-gradient(135deg, #374151, #1f2937);
    color: #d1d5db;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-professional.btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563, #374151);
    color: #f9fafb;
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-professional.btn-tertiary {
    background: linear-gradient(135deg, #64748b, #475569);
    color: #f1f5f9;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-professional.btn-tertiary:hover {
    background: linear-gradient(135deg, #475569, #334155);
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-professional .btn-icon {
    font-size: 1.1rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}



.mod-path-delete {
    color: #94a3b8;
    font-size: 0.9rem;
    font-family: 'Courier New', monospace;
    word-break: break-all;
    background: rgba(0, 0, 0, 0.2);
    padding: 0.5rem;
    border-radius: 6px;
    margin: 0;
}

.warning-message-professional {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1.5rem;
    color: #f59e0b;
    font-weight: 600;
}

.warning-icon-small {
    font-size: 1.1rem;
}

/* Button styling for custom modals */
.custom-modal-footer .btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    border: none;
    color: white;
}

.custom-modal-footer .btn-danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
}

/* Responsive Design for Modals */
@media (max-width: 768px) {
    .upload-area {
        padding: 2rem 1rem;
    }
    
    .upload-area h4 {
        font-size: 1.1rem;
    }
    
    .upload-area p {
        font-size: 0.9rem;
    }
    
    .supported-formats {
        gap: 0.3rem;
    }
    
    .format-tag {
        font-size: 0.75rem;
        padding: 0.25rem 0.6rem;
    }
    
    .files-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .file-item-professional {
        padding: 0.8rem;
    }
    
    .file-item-professional:hover {
        transform: none;
    }
    
    .warning-icon {
        font-size: 3rem;
    }
    
    .delete-message p {
        font-size: 1rem;
    }
}

/* No Mods Message Styling */
.no-mods-message {
    text-align: center;
    padding: 4rem 2rem;
    color: #94a3b8;
}

.no-mods-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.7;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.no-mods-message h3 {
    color: #f1f5f9;
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
}

.no-mods-message p {
    color: #94a3b8;
    font-size: 1rem;
    margin: 0 0 2rem 0;
    line-height: 1.6;
}

.no-mods-message .btn {
    background: linear-gradient(135deg, var(--primary-color), #2563eb);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.no-mods-message .btn:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* Vertical Sidebar Layout for Mod Categories - REMOVED DUPLICATE */

.categories-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.8rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
}

.categories-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.total-categories {
    color: #94a3b8;
    font-size: 0.9rem;
    font-weight: 500;
}

.categories-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    overflow-x: hidden;
}

.category-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.category-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: var(--primary-color);
    transform: translateX(5px);
}

.category-item.active {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(6, 182, 212, 0.1));
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.2);
}

.category-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
}

.category-icon {
    font-size: 1.5rem;
    min-width: 2rem;
    text-align: center;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.category-info {
    flex: 1;
    min-width: 0;
}

.category-name {
    display: block;
    color: #f1f5f9;
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.3rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.category-count {
    color: #94a3b8;
    font-size: 0.8rem;
    font-weight: 500;
}

.category-item.active .category-name {
    color: var(--primary-color);
}

.category-item.active .category-count {
    color: #06b6d4;
}

/* Mod Content Area - MOVED TO CONSOLIDATED SECTION */

.category-content-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1.5rem;
    background: linear-gradient(135deg, #1e293b, #0f172a);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    flex-shrink: 0;
}

.active-category-info h3 {
    margin: 0 0 0.5rem 0;
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.active-category-info p {
    margin: 0;
    color: #94a3b8;
    font-size: 1rem;
    line-height: 1.5;
    max-width: 600px;
}

.category-stats {
    text-align: right;
}

.category-stats span {
    background: linear-gradient(135deg, var(--accent-color), #10b981);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

/* Category Mods Container - MOVED TO CONSOLIDATED SECTION */

/* Responsive Design for Sidebar Layout */
@media (max-width: 1200px) {
    .mod-categories-sidebar {
        flex: 0 0 250px;
    }
}

@media (max-width: 768px) {
    .mod-sidebar-layout {
        flex-direction: column;
        height: auto;
        min-height: auto;
        max-height: none;
    }
    
    .mod-categories-sidebar {
        flex: none;
        height: auto;
        padding: 1rem;
    }
    
    .mod-content-area {
        height: 60vh;
        min-height: 400px;
    }
    
    .categories-list {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        gap: 0.4rem;
        flex: none;
    }
    
    .category-item {
        padding: 0.6rem;
    }
    
    .category-item:hover {
        transform: none;
    }
    
    .category-content-header {
        flex-direction: column;
        gap: 1rem;
        padding: 1.5rem;
    }
    
    .category-stats {
        text-align: left;
    }
    
    .mod-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
        padding: 1.5rem;
    }
    
    .mod-actions {
        margin-left: 0;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .categories-list {
        grid-template-columns: 1fr;
    }
    
    .category-content-header {
        padding: 1rem;
    }
    
    .category-mods-container {
        padding: 1rem;
    }
    
    .mod-info h4 {
        font-size: 1.3rem;
    }
    
    .mod-item {
        padding: 1rem;
    }
}

/* CLEAN MOD SIDEBAR LAYOUT - NO SCROLLING ISSUES */
.mod-sidebar-layout {
    display: flex;
    gap: 1rem;
    height: 70vh;
    min-height: 500px;
    max-height: 80vh;
    overflow: hidden;
    margin: 0;
    padding: 0;
    position: relative;
    box-sizing: border-box;
}

.mod-categories-sidebar {
    flex: 0 0 300px;
    background: linear-gradient(135deg, #1e293b, #0f172a);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
}

.mod-content-area {
    flex: 1;
    min-width: 0;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-sizing: border-box;
    min-height: 0;
}

.category-mods-container {
    padding: 1rem;
    overflow-y: auto;
    overflow-x: hidden;
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Hide scrollbar for categories if content overflows */
.categories-list {
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: var(--border-color) transparent;
}

.categories-list::-webkit-scrollbar {
    width: 4px;
}

.categories-list::-webkit-scrollbar-track {
    background: transparent;
}

.categories-list::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 2px;
}

/* Clean mod grid without conflicting overflow rules */
.mod-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    padding: 0;
    margin: 0;
}

.mod-manager-container {
    overflow: hidden;
}

.mod-content {
    overflow: hidden;
}

/* New Mod Manager Options Interface */
.mod-manager-options {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
    padding: 2rem;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    max-width: 800px;
    width: 100%;
}

.option-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 2.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.option-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, transparent, rgba(255, 255, 255, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.option-card:hover::before {
    opacity: 1;
}

.option-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.option-card.preset-games:hover {
    border-color: rgba(59, 130, 246, 0.6);
    box-shadow: 0 15px 35px rgba(59, 130, 246, 0.2);
}

.option-card.add-game:hover {
    border-color: rgba(16, 185, 129, 0.6);
    box-shadow: 0 15px 35px rgba(16, 185, 129, 0.2);
}

.option-card.remove-game:hover {
    border-color: rgba(239, 68, 68, 0.6);
    box-shadow: 0 15px 35px rgba(239, 68, 68, 0.2);
}

.option-card.help:hover {
    border-color: rgba(139, 92, 246, 0.6);
    box-shadow: 0 15px 35px rgba(139, 92, 246, 0.2);
}

.option-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
    transition: all 0.3s ease;
}

.option-card:hover .option-icon {
    transform: scale(1.1) rotate(5deg);
}

.option-card h3 {
    color: #f1f5f9;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    transition: all 0.3s ease;
}

.option-card:hover h3 {
    color: #ffffff;
    transform: translateY(-2px);
}

.option-card p {
    color: #94a3b8;
    font-size: 1rem;
    font-weight: 500;
    margin: 0 0 0.5rem 0;
    transition: all 0.3s ease;
}

.option-card:hover p {
    color: #cbd5e1;
}

.option-subtitle {
    color: #64748b;
    font-size: 0.85rem;
    font-weight: 400;
    font-style: italic;
    margin: 0;
    transition: all 0.3s ease;
}

.option-card:hover .option-subtitle {
    color: #94a3b8;
}

/* Games Header */
.games-header {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #1e293b, #0f172a);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
}

.back-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #64748b, #475569);
    color: #f1f5f9;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: linear-gradient(135deg, #475569, #334155);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.games-header h3 {
    color: #f1f5f9;
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Game Cards in Selection View */
.game-selector .game-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.game-selector .game-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.game-selector .game-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

.game-selector .game-card.selected {
    border-color: var(--primary-color);
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.3);
}

.game-selector .game-card img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 12px;
    margin-bottom: 1rem;
}

.game-selector .game-card .game-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.game-selector .game-card h4 {
    color: #f1f5f9;
    font-size: 1.3rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.game-selector .game-card p {
    color: #94a3b8;
    font-size: 0.9rem;
    margin: 0;
}

/* Responsive Design for Options */
@media (max-width: 768px) {
    .options-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        max-width: 400px;
    }
    
    .option-card {
        padding: 2rem;
        min-height: 160px;
    }
    
    .option-icon {
        font-size: 2.5rem;
    }
    
    .option-card h3 {
        font-size: 1.3rem;
    }
    
    .games-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .game-selector .game-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .mod-manager-options {
        padding: 1rem;
        min-height: 50vh;
    }
    
    .option-card {
        padding: 1.5rem;
        min-height: 140px;
    }
    
    .option-icon {
        font-size: 2rem;
        margin-bottom: 0.75rem;
    }
    
    .option-card h3 {
        font-size: 1.1rem;
    }
    
    .option-card p {
        font-size: 0.9rem;
    }
    
    .option-subtitle {
        font-size: 0.8rem;
    }
    
    .games-header h3 {
        font-size: 1.5rem;
    }
    
    .back-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

/* Add Game Dialog Styling */
.add-game-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
}

.form-section {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-section label {
    color: #f1f5f9;
    font-weight: 600;
    font-size: 0.95rem;
}

.form-help {
    color: #9ca3af;
    font-size: 12px;
    margin-top: 6px;
    margin-bottom: 0;
    line-height: 1.4;
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 6px;
    padding: 8px 12px;
}

.form-help strong {
    color: #3b82f6;
    font-weight: 600;
}

.path-input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.path-input-group .modal-input {
    flex: 1;
}

.path-input-group .btn {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
    white-space: nowrap;
}

.mod-folder-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.mod-folder-item .modal-input {
    margin: 0;
}

.custom-game-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: center;
}

.custom-game-card {
    position: relative;
}

.delete-game-content {
    text-align: center;
    padding: 1rem;
}

.delete-game-content .warning-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.delete-message p {
    margin: 0.5rem 0;
    line-height: 1.5;
}

.delete-message p:first-child {
    font-size: 1.1rem;
    color: #f1f5f9;
}

.delete-message p:last-child {
    color: #94a3b8;
    font-size: 0.95rem;
}

/* Form responsive design */
@media (max-width: 768px) {
    .add-game-form {
        gap: 1rem;
    }
    
    .mod-folder-item {
        padding: 0.75rem;
        gap: 0.5rem;
    }
    
    .path-input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .path-input-group .btn {
        width: 100%;
    }
}

/* File types dropdown styling */
.file-types-section {
    margin-top: 10px;
}

.file-types-section label {
    display: block;
    margin-bottom: 5px;
    color: #e5e7eb;
    font-size: 14px;
    font-weight: 500;
}

.file-types-dropdown {
    position: relative;
    width: 100%;
}

.dropdown-header {
    background: rgba(31, 41, 55, 0.8);
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 10px 12px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: all 0.3s ease;
}

.dropdown-header:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.dropdown-header .selected-text {
    color: #e5e7eb;
    font-size: 14px;
}

.dropdown-header .dropdown-arrow {
    color: #9ca3af;
    font-size: 12px;
    transition: transform 0.3s ease;
}

.dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: rgba(17, 24, 39, 0.95);
    border: 1px solid #374151;
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
}

.file-type-option {
    display: flex !important;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin: 0 !important;
}

.file-type-option:hover {
    background: rgba(59, 130, 246, 0.1);
}

.file-type-option input[type="checkbox"] {
    margin-right: 8px;
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
}

.file-type-option span {
    color: #e5e7eb;
    font-size: 14px;
    user-select: none;
}

/* Custom scrollbar for dropdown */
.dropdown-options::-webkit-scrollbar {
    width: 6px;
}

.dropdown-options::-webkit-scrollbar-track {
    background: rgba(31, 41, 55, 0.5);
}

.dropdown-options::-webkit-scrollbar-thumb {
    background: #3b82f6;
    border-radius: 3px;
}

.dropdown-options::-webkit-scrollbar-thumb:hover {
    background: #2563eb;
}

/* Close dropdown when clicking outside */
.file-types-dropdown.open .dropdown-options {
    display: block;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .dropdown-header {
        padding: 12px;
    }
    
    .file-type-option {
        padding: 10px 12px;
    }
    
    .file-type-option span {
        font-size: 15px;
    }
}

/* === NEW STEAM-LIKE MOD MANAGER INTERFACE === */

/* Main Layout */
.mod-manager-new-layout {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 70vh;
    gap: 1rem;
    padding: 1rem;
}

/* === MOD SETS MANAGEMENT === */

.minecraft-modsets-section {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.08) 0%, rgba(29, 78, 216, 0.05) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.minecraft-modsets-section:hover {
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1);
}

.modsets-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.75rem;
}

.modsets-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    width: 100%;
}

.modsets-info h4 {
    color: #3b82f6;
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modsets-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex: 1;
    justify-content: flex-end;
}

.modsets-dropdown {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.8) 100%);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    color: #f1f5f9;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    min-width: 200px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modsets-dropdown:hover {
    border-color: rgba(59, 130, 246, 0.5);
    background: linear-gradient(135deg, rgba(15, 23, 42, 1) 0%, rgba(30, 41, 59, 0.9) 100%);
}

.modsets-dropdown:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.modsets-dropdown option {
    background: #1e293b;
    color: #f1f5f9;
}

.modsets-help-text {
    margin-bottom: 0.75rem;
}

.modsets-help-text small {
    color: #94a3b8;
    font-size: 0.85rem;
    line-height: 1.4;
}

.modsets-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    background: rgba(15, 23, 42, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.status-indicator {
    font-size: 0.8rem;
    width: 12px;
    height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.status-text {
    color: #e2e8f0;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Active mod set styling */
.modsets-status.active {
    background: rgba(34, 197, 94, 0.1);
    border-color: rgba(34, 197, 94, 0.3);
}

.modsets-status.active .status-indicator {
    color: #22c55e;
}

.modsets-status.active .status-text {
    color: #86efac;
}

/* Switching mod set styling */
.modsets-status.switching {
    background: rgba(251, 191, 36, 0.1);
    border-color: rgba(251, 191, 36, 0.3);
}

.modsets-status.switching .status-indicator {
    color: #fbbf24;
    animation: pulse 1.5s ease-in-out infinite;
}

.modsets-status.switching .status-text {
    color: #fde68a;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Game Search Bar */
.game-search-bar {
    margin: 1rem 0 1.5rem 0;
    display: flex;
    justify-content: center;
    padding: 0 1rem;
}

.game-search-bar .search-container-professional {
    max-width: 450px;
    width: 100%;
}

.game-search-bar .search-input-wrapper {
    position: relative;
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.9) 100%);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.game-search-bar .search-input-wrapper:hover {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.1);
}

.game-search-bar .search-input-wrapper:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2), 0 6px 20px rgba(59, 130, 246, 0.15);
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 1) 100%);
    transform: translateY(-1px);
}

.game-search-bar #game-search {
    background: transparent;
    border: none;
    color: #f1f5f9;
    font-size: 1rem;
    font-weight: 500;
    width: 100%;
    padding: 0.875rem 3rem 0.875rem 3rem;
}

.game-search-bar #game-search::placeholder {
    color: #64748b;
    font-weight: 400;
}

.game-search-bar #game-search:focus {
    outline: none;
}

.game-search-bar .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    font-size: 1.1rem;
    transition: color 0.3s ease;
    pointer-events: none;
}

.game-search-bar .search-input-wrapper:focus-within .search-icon {
    color: #3b82f6;
}

.game-search-bar #game-search-clear {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 6px;
    width: 24px;
    height: 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    opacity: 0;
    transition: all 0.3s ease, opacity 0.15s ease;
}

.game-search-bar #game-search-clear:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
    transform: translateY(-50%) scale(1.15);
}

/* Mod Filter Section - Search bar when viewing individual games */
.mod-filter-section {
    margin: 1.5rem 0 2rem 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 1.5rem;
    padding: 0 1rem;
    position: relative;
}

.mod-filter-section .search-container-professional {
    width: 400px;
    flex-shrink: 0;
    margin: 0 auto;
}

.mod-filter-section .search-input-wrapper {
    position: relative;
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.9) 100%);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.mod-filter-section .search-input-wrapper:hover {
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.1);
}

.mod-filter-section .search-input-wrapper:focus-within {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2), 0 6px 20px rgba(59, 130, 246, 0.15);
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 1) 100%);
    transform: translateY(-1px);
}

.mod-filter-section #mod-search {
    background: transparent;
    border: none;
    color: #f1f5f9;
    font-size: 1rem;
    font-weight: 500;
    width: 100%;
    padding: 0.875rem 3rem 0.875rem 3rem;
}

.mod-filter-section #mod-search::placeholder {
    color: #64748b;
    font-weight: 400;
}

.mod-filter-section #mod-search:focus {
    outline: none;
}

.mod-filter-section .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    font-size: 1.1rem;
    transition: color 0.3s ease;
    pointer-events: none;
}

.mod-filter-section .search-input-wrapper:focus-within .search-icon {
    color: #3b82f6;
}

.mod-filter-section .search-clear {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 6px;
    width: 24px;
    height: 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mod-filter-section .search-clear:hover {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.5);
    transform: translateY(-50%) scale(1.15);
}

.filter-stats {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.6) 0%, rgba(30, 41, 59, 0.7) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0.5rem 0.875rem;
    transition: all 0.3s ease;
    cursor: default;
    white-space: nowrap;
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
}

.filter-stats:hover {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.9) 100%);
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateY(-1px);
}

.filter-stats .stats-text {
    color: #94a3b8;
    font-size: 0.9rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.filter-stats:hover .stats-text {
    color: #cbd5e1;
}

/* Top Controls Bar */
.mod-manager-controls {
    display: flex;
    justify-content: flex-end;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.mod-manager-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

/* Compact Action Buttons */
.btn-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    font-weight: 600;
    border: 1px solid transparent;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    white-space: nowrap;
}

.btn-compact.btn-primary {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    color: white;
    border-color: rgba(59, 130, 246, 0.3);
}

.btn-compact.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-compact.btn-secondary {
    background: linear-gradient(135deg, #64748b, #475569);
    color: white;
    border-color: rgba(100, 116, 139, 0.3);
}

.btn-compact.btn-secondary:hover {
    background: linear-gradient(135deg, #475569, #334155);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(100, 116, 139, 0.3);
}

.btn-compact.btn-tertiary {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    border-color: rgba(139, 92, 246, 0.3);
}

.btn-compact.btn-tertiary:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.btn-compact .btn-icon {
    font-size: 0.9rem;
}

/* Sort and Filter Controls */
.game-controls-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.8), rgba(15, 23, 42, 0.8));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    flex-wrap: wrap;
    gap: 1rem;
}

.sort-filter-section {
    display: flex;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.sort-control, .filter-control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-control label, .filter-control label {
    color: #e2e8f0;
    font-size: 0.9rem;
    font-weight: 600;
    white-space: nowrap;
}

.sort-control select, .filter-control select {
    background: rgba(30, 41, 59, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    color: #e2e8f0;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 140px;
}

.sort-control select:hover, .filter-control select:hover {
    border-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.sort-control select:focus, .filter-control select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Fix dropdown option styling */
.sort-control select option, .filter-control select option {
    background: #1e293b;
    color: #e2e8f0;
    padding: 0.5rem;
    border: none;
}

.sort-control select option:hover, .filter-control select option:hover {
    background: #3b82f6;
    color: #ffffff;
}

.sort-control select option:checked, .filter-control select option:checked {
    background: #3b82f6;
    color: #ffffff;
}

.view-options {
    display: flex;
    align-items: center;
}

.toggle-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #e2e8f0;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: color 0.3s ease;
}

.toggle-option:hover {
    color: #3b82f6;
}

.toggle-option input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #3b82f6;
    cursor: pointer;
}

.game-count {
    color: #94a3b8;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Games Library Grid */
.games-library {
    flex: 1;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.5), rgba(15, 23, 42, 0.5));
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    overflow-y: auto;
    min-height: 400px;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

/* Steam-like Game Cards */
.steam-game-card {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9));
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    backdrop-filter: blur(10px);
}

.steam-game-card:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 30px rgba(59, 130, 246, 0.2);
}

.steam-game-card.preset-game:hover {
    border-color: rgba(16, 185, 129, 0.5);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 30px rgba(16, 185, 129, 0.2);
}

.steam-game-card.custom-game:hover {
    border-color: rgba(139, 92, 246, 0.5);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4), 0 0 30px rgba(139, 92, 246, 0.2);
}

/* Game Image Container */
.game-image-container {
    position: relative;
    width: 100%;
    height: 160px;
    overflow: hidden;
}

.game-cover {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.steam-game-card:hover .game-cover {
    transform: scale(1.05);
}

/* Small icon handling with gradient background */
.game-image-container.small-icon {
    background: linear-gradient(135deg, #1e293b 0%, #3b82f6 50%, #06b6d4 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.game-image-container.small-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
}

.game-image-container.small-icon .game-cover {
    object-fit: contain;
    max-width: 80px;
    max-height: 80px;
    width: auto;
    height: auto;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    position: relative;
    z-index: 2;
}

.steam-game-card:hover .game-image-container.small-icon .game-cover {
    transform: scale(1.05);
    box-shadow: 0 6px 20px rgba(59,130,246,0.4);
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.2), rgba(0,0,0,0.8));
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.75rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.steam-game-card:hover .game-overlay {
    opacity: 1;
}

.game-type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.7rem;
    font-weight: 700;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    backdrop-filter: blur(10px);
}

.preset-badge {
    background: rgba(16, 185, 129, 0.9);
    color: white;
    border: 1px solid rgba(16, 185, 129, 0.5);
}

.custom-badge {
    background: rgba(139, 92, 246, 0.9);
    color: white;
    border: 1px solid rgba(139, 92, 246, 0.5);
}

.play-button {
    background: rgba(59, 130, 246, 0.9);
    border: 1px solid rgba(59, 130, 246, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.play-button:hover {
    background: rgba(59, 130, 246, 1);
    transform: scale(1.1);
}

.play-icon {
    color: white;
    font-size: 1rem;
    margin-left: 2px;
}

/* Game Info Section */
.game-info {
    padding: 1rem;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.7), rgba(15, 23, 42, 0.7));
}

.game-title {
    color: #f1f5f9;
    font-size: 1.1rem;
    font-weight: 700;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    line-height: 1.3;
}

.game-description {
    color: #94a3b8;
    font-size: 0.85rem;
    margin: 0 0 0.5rem 0;
    line-height: 1.4;
}

.last-played {
    color: #64748b;
    font-size: 0.75rem;
    font-style: italic;
    margin: 0;
}

/* No Games Message */
.no-games-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 4rem 2rem;
    color: #e2e8f0;
    background: linear-gradient(135deg, rgba(17, 24, 39, 0.6) 0%, rgba(31, 41, 55, 0.8) 100%);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin: 2rem auto;
    width: 100%;
    max-width: 600px;
}

.no-games-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.7;
}

.no-games-message h3 {
    color: #f1f5f9;
    font-size: 1.5rem;
    margin: 0 0 0.5rem 0;
    font-weight: 600;
}

.no-games-message p {
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
    color: #94a3b8;
    max-width: 400px;
}

/* Back to Library Button */
.mod-header-main .back-btn {
    background: linear-gradient(135deg, #64748b, #475569);
    color: #f1f5f9;
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-right: 1rem;
}

.mod-header-main .back-btn:hover {
    background: linear-gradient(135deg, #475569, #334155);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .games-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .mod-manager-new-layout {
        padding: 0.5rem;
        gap: 0.75rem;
    }
    
    .game-search-bar {
        margin: 0.75rem 0 1rem 0;
        padding: 0 0.5rem;
    }
    
    .game-search-bar .search-container-professional {
        max-width: 100%;
    }
    
    .game-search-bar #game-search {
        font-size: 0.9rem;
        padding: 0.75rem 2.5rem;
    }
    
    .game-search-bar .search-icon {
        left: 0.75rem;
        font-size: 1rem;
    }
    
    .game-search-bar #game-search-clear {
        right: 0.75rem;
        width: 20px;
        height: 20px;
        font-size: 12px;
    }
    
    /* Mobile responsiveness for mod filter section */
    .mod-filter-section {
        margin: 1rem 0 1.5rem 0;
        padding: 0 0.5rem;
        flex-direction: column;
        gap: 1rem;
    }
    
    .mod-filter-section .search-container-professional {
        max-width: 100%;
    }
    
    .mod-filter-section #mod-search {
        font-size: 0.9rem;
        padding: 0.75rem 2.5rem;
    }
    
    .mod-filter-section .search-icon {
        left: 0.75rem;
        font-size: 1rem;
    }
    
    .mod-filter-section .search-clear {
        right: 0.75rem;
        width: 20px;
        height: 20px;
        font-size: 12px;
    }
    
    .mod-filter-section .search-clear:hover {
        transform: translateY(-50%) scale(1.15);
    }
    
    .filter-stats {
        padding: 0.4rem 0.75rem;
    }
    
    .filter-stats .stats-text {
        font-size: 0.85rem;
    }
    
    .mod-manager-actions {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .btn-compact {
        padding: 0.5rem 0.75rem;
        font-size: 0.85rem;
    }
    
    .btn-compact span:not(.btn-icon) {
        display: none;
    }
    
    .game-controls-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
        padding: 0.75rem;
    }
    
    .sort-filter-section {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .sort-control, .filter-control {
        flex-direction: column;
        align-items: stretch;
        gap: 0.25rem;
    }
    
    .sort-control select, .filter-control select {
        min-width: auto;
        width: 100%;
    }
    
    .games-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 0.75rem;
        padding: 0.5rem;
    }
    
    .game-image-container {
        height: 120px;
    }
    
    .game-info {
        padding: 0.75rem;
    }
    
    .game-title {
        font-size: 1rem;
    }
    
    .game-description {
        font-size: 0.8rem;
    }
    
    .mod-header-main .back-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .games-grid {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .game-image-container {
        height: 100px;
    }
    
    .game-info {
        padding: 0.5rem;
    }
    
    .game-title {
        font-size: 0.9rem;
    }
    
    .game-description {
        font-size: 0.75rem;
    }
    
    .no-games-message {
        padding: 2rem 1rem;
    }
    
    .no-games-icon {
        font-size: 3rem;
    }
    
    .no-games-message h3 {
        font-size: 1.2rem;
    }
    
    .no-games-message p {
        font-size: 0.9rem;
    }
}

/* === NO MODS MESSAGE STYLING === */
.no-mods-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    text-align: center;
    color: #e2e8f0;
    background: linear-gradient(135deg, rgba(17, 24, 39, 0.6) 0%, rgba(31, 41, 55, 0.8) 100%);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin: 2rem 0;
}

.no-mods-container .no-mods-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.7;
}

.no-mods-container h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #f1f5f9;
}

.no-mods-container p {
    font-size: 1rem;
    margin-bottom: 2rem;
    color: #94a3b8;
    max-width: 500px;
}

.no-mods-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    justify-content: center;
}

.no-mods-help {
    padding: 1rem;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    max-width: 600px;
}

.no-mods-help p {
    margin: 0;
    font-size: 0.9rem;
    color: #bfdbfe;
}

/* Professional Edit Button */
.edit-button-pro {
    position: absolute;
    bottom: 8px;
    left: 8px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.9), rgba(37, 99, 235, 0.9));
    border: 1px solid rgba(59, 130, 246, 0.8);
    border-radius: 8px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(6px);
    z-index: 10;
    display: flex;
    align-items: center;
    gap: 6px;
    min-width: 70px;
    justify-content: center;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.25);
}

.edit-button-pro:hover {
    background: linear-gradient(135deg, rgba(59, 130, 246, 1), rgba(37, 99, 235, 1));
    border-color: rgba(59, 130, 246, 1);
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
}

.edit-button-pro .edit-icon {
    font-size: 1rem;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.edit-button-pro .edit-text {
    font-size: 0.85rem;
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Show edit button on hover for custom games */
.steam-game-card:not(.preset-game):not(.delete-mode):hover .edit-button-pro {
    opacity: 1;
    transform: translateY(0);
}

/* Remove Mode - Full Card Red Highlight with Center Trash Icon */
.steam-game-card.delete-mode {
    border-color: #dc2626;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.5);
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.15), rgba(185, 28, 28, 0.1));
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transform: translateY(-2px);
}

.steam-game-card.delete-mode::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(220, 38, 38, 0.3) 0%, rgba(220, 38, 38, 0.1) 70%);
    pointer-events: none;
    z-index: 1;
    animation: pulseRedBackground 2s infinite ease-in-out;
}

@keyframes pulseRedBackground {
    0%, 100% {
        background: radial-gradient(circle at center, rgba(220, 38, 38, 0.3) 0%, rgba(220, 38, 38, 0.1) 70%);
    }
    50% {
        background: radial-gradient(circle at center, rgba(220, 38, 38, 0.4) 0%, rgba(220, 38, 38, 0.15) 70%);
    }
}

.steam-game-card.delete-mode:hover {
    border-color: #b91c1c;
    box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.7);
    background: linear-gradient(135deg, rgba(220, 38, 38, 0.25), rgba(185, 28, 28, 0.15));
    transform: translateY(-4px);
}

/* Center trash icon overlay for remove mode */
.delete-mode-center-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(220, 38, 38, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 15;
    backdrop-filter: blur(2px);
}

.steam-game-card.delete-mode:hover .delete-mode-center-overlay {
    opacity: 1;
}

.delete-icon-large {
    font-size: 3rem;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    margin-bottom: 8px;
    animation: trashBounce 0.6s ease-in-out;
}

@keyframes trashBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.delete-text-large {
    font-size: 1rem;
    color: white;
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
    text-align: center;
    opacity: 0.9;
}

/* Red styling for game info in delete mode */
.steam-game-card.delete-mode .game-title {
    color: #f87171 !important;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
}

.steam-game-card.delete-mode .game-description {
    color: #fca5a5 !important;
    position: relative;
    z-index: 2;
}

.steam-game-card.delete-mode .last-played {
    color: #fecaca !important;
    position: relative;
    z-index: 2;
}

/* Global delete mode styling */
body.delete-mode-active .steam-game-card.preset-game {
    opacity: 0.4;
    pointer-events: none;
    filter: grayscale(0.7);
    transition: all 0.3s ease;
}

body.delete-mode-active .steam-game-card.preset-game .game-title {
    color: #6b7280 !important;
}

body.delete-mode-active .steam-game-card.preset-game .game-description {
    color: #9ca3af !important;
}

/* Steam and Xbox Game Confirmation Dialogs */
.steam-confirmation, .xbox-confirmation {
    text-align: center;
    padding: 1rem;
}

.confirmation-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.steam-confirmation h3, .xbox-confirmation h3 {
    color: #3b82f6;
    margin-bottom: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
}

.steam-options, .xbox-info {
    margin: 1.5rem 0;
}

.option-explanation {
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 1rem;
    text-align: left;
    margin-bottom: 1rem;
}

.option-explanation strong {
    color: #3b82f6;
    display: block;
    margin-bottom: 0.5rem;
}

.option-explanation ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.5rem;
}

.option-explanation li {
    margin-bottom: 0.5rem;
    color: #e5e7eb;
}

.path-examples {
    text-align: left;
    margin-top: 1rem;
}

.path-box {
    background: rgba(31, 41, 55, 0.8);
    border: 1px solid rgba(75, 85, 99, 0.5);
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.path-box strong {
    color: #3b82f6;
    display: block;
    margin-bottom: 0.5rem;
}

.path-box code {
    display: block;
    background: rgba(0, 0, 0, 0.3);
    color: #10b981;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    margin: 0.25rem 0;
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.path-box ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.5rem;
}

.path-box li {
    margin-bottom: 0.25rem;
    color: #d1d5db;
}

.info-box {
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
    text-align: left;
}

.info-box.warning {
    background: rgba(245, 158, 11, 0.1);
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.info-box strong {
    color: #f59e0b;
    display: block;
    margin-bottom: 0.5rem;
}

.info-box ul {
    margin: 0.5rem 0 0 0;
    padding-left: 1.5rem;
}

.info-box li {
    margin-bottom: 0.5rem;
    color: #e5e7eb;
}

.confirmation-buttons, .reselect-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.confirmation-buttons .btn, .reselect-buttons .btn {
    min-width: 180px;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.confirmation-buttons .btn:hover, .reselect-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Game Type Confirmation Dialog */
.game-type-confirmation {
    text-align: center;
    padding: 1rem;
}

.detection-info {
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.detection-info strong {
    color: #3b82f6;
}

.type-option-explanation {
    margin: 1.5rem 0;
    color: #d1d5db;
}

.type-options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
    max-width: 900px;
    margin-left: auto;
    margin-right: auto;
}

.type-option {
    background: linear-gradient(135deg, rgba(31, 41, 55, 0.9), rgba(17, 24, 39, 0.9));
    border: 2px solid rgba(75, 85, 99, 0.3);
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    text-align: center;
    min-height: 140px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.type-option:hover {
    border-color: #3b82f6;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.15));
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.25);
}

.type-option.suggested {
    border-color: #10b981;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
}

.type-option.suggested:hover {
    border-color: #10b981;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.2));
    box-shadow: 0 8px 24px rgba(16, 185, 129, 0.25);
}

.type-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.type-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #e5e7eb;
    margin-bottom: 0.5rem;
}

.type-option.suggested .type-title {
    color: #10b981;
}

.type-description {
    font-size: 0.85rem;
    color: #9ca3af;
    line-height: 1.4;
    text-align: center;
}

.suggestion-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.confirmation-note {
    background: rgba(245, 158, 11, 0.05);
    border: 1px solid rgba(245, 158, 11, 0.2);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1.5rem;
    text-align: left;
}

.confirmation-note strong {
    color: #f59e0b;
}

/* Responsive design for confirmation dialogs */
@media (max-width: 768px) {
    .confirmation-buttons, .reselect-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .confirmation-buttons .btn, .reselect-buttons .btn {
        width: 100%;
        max-width: 300px;
    }
    
    .path-box code {
        word-break: break-all;
        font-size: 0.75rem;
    }
    
    /* Game type confirmation responsive */
    .type-options-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .type-option {
        padding: 1rem;
        min-height: auto;
    }
    
    .type-icon {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
    }
    
    .type-title {
        font-size: 1rem;
    }
    
    .type-description {
        font-size: 0.8rem;
    }
}

/* --- Custom View Transition Animations (Mod Manager) --- */
@keyframes slideOutLeft {
  0% { opacity: 1; transform: translateX(0); }
  100% { opacity: 0; transform: translateX(-80px) scale(0.95); }
}

@keyframes slideInRight {
  0% { opacity: 0; transform: translateX(80px) scale(0.95); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes slideOutRight {
  0% { opacity: 1; transform: translateX(0); }
  100% { opacity: 0; transform: translateX(80px) scale(0.95); }
}

@keyframes slideInLeft {
  0% { opacity: 0; transform: translateX(-80px) scale(0.95); }
  100% { opacity: 1; transform: translateX(0); }
}

@keyframes fadeInQuick {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes fadeOutQuick {
  0% { opacity: 1; }
  100% { opacity: 0; }
}

.slide-out-left {
  animation: slideOutLeft 0.45s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
}

.slide-in-right {
  animation: slideInRight 0.45s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
}

.slide-out-right {
  animation: slideOutRight 0.45s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
}

.slide-in-left {
  animation: slideInLeft 0.45s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
}

.fade-in-fast {
  animation: fadeInQuick 0.25s ease-out forwards;
}

.fade-out-fast {
  animation: fadeOutQuick 0.25s ease-in forwards;
}
/* --- End View Transition Animations --- */

/* --- Interactive Card Depth & Button Ripple --- */
.steam-game-card {
  perspective: 800px; /* enable 3D space for children */
}

.steam-game-card .game-image-container,
.steam-game-card .game-info {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  transform-style: preserve-3d;
  will-change: transform;
}

.steam-game-card.tilt-active .game-image-container,
.steam-game-card.tilt-active .game-info {
  box-shadow: 0 12px 25px rgba(0,0,0,0.35);
}

/* Subtle layered glow on hover */
.steam-game-card::after {
  content: '';
  position: absolute;
  top: -10%;
  left: -10%;
  width: 120%;
  height: 120%;
  background: radial-gradient(circle at center, rgba(59,130,246,0.2), transparent 60%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
}

.steam-game-card:hover::after {
  opacity: 1;
}

/* Ripple effect */
.btn, .btn-compact, .btn-professional {
  position: relative;
  overflow: hidden;
}

.btn::after, .btn-compact::after, .btn-professional::after {
  content: '';
  position: absolute;
  background: rgba(255,255,255,0.35);
  border-radius: 50%;
  transform: scale(0);
  width: 100px;
  height: 100px;
  opacity: 0;
  pointer-events: none;
  transition: transform 0.6s, opacity 1s;
}

.btn:active::after,
.btn-compact:active::after,
.btn-professional:active::after {
  transform: scale(3);
  opacity: 0;
  transition:  transform 0.6s, opacity 1s;
}
/* --- End Interactive Enhancements --- */

/* Remove 3D tilt and add advanced animations */
/* --- Remove 3D Tilt --- */
.steam-game-card {
  /* Remove perspective and tilt styles */
  /* perspective: 800px; */
}
.steam-game-card .game-image-container,
.steam-game-card .game-info {
  /* Remove transform-style and will-change */
  transition: box-shadow 0.3s ease;
}
.steam-game-card.tilt-active .game-image-container,
.steam-game-card.tilt-active .game-info {
  /* Remove tilt-active shadow */
  box-shadow: none;
}

/* --- Shimmer on hover --- */
.steam-game-card::before {
  content: '';
  position: absolute;
  top: 0; left: -75%;
  width: 50%; height: 100%;
  background: linear-gradient(120deg, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0.25) 50%, rgba(255,255,255,0.08) 100%);
  z-index: 2;
  pointer-events: none;
  transition: none;
  opacity: 0;
}
.steam-game-card:hover::before {
  animation: shimmer-move 1.2s cubic-bezier(0.4,0,0.2,1) 1;
  opacity: 1;
}
@keyframes shimmer-move {
  0% { left: -75%; opacity: 0; }
  10% { opacity: 1; }
  60% { left: 100%; opacity: 1; }
  100% { left: 100%; opacity: 0; }
}

/* --- Particle burst effect for selection --- */
.steam-game-card.selected {
  box-shadow: 0 0 0 4px var(--primary-color), 0 0 40px 10px rgba(59,130,246,0.15);
  animation: card-pop 0.5s cubic-bezier(0.4,0,0.2,1);
}
@keyframes card-pop {
  0% { transform: scale(1); }
  60% { transform: scale(1.08); }
  100% { transform: scale(1); }
}

.particle-burst {
  position: absolute;
  pointer-events: none;
  left: 50%; top: 50%;
  width: 0; height: 0;
  z-index: 99;
}
.particle {
  position: absolute;
  width: 8px; height: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6, #06b6d4);
  opacity: 0.8;
  pointer-events: none;
  animation: particle-burst-anim 0.7s forwards;
}
@keyframes particle-burst-anim {
  0% { transform: scale(0.5) translate(0,0); opacity: 1; }
  80% { opacity: 1; }
  100% { transform: scale(1.2) translate(var(--dx), var(--dy)); opacity: 0; }
}

/* Simple mod content visibility control */
.mod-content {
  display: none;
  opacity: 0;
  background: var(--bg-primary);
}

.mod-content.active {
  display: block;
  opacity: 1;
}

#mod-manager-main {
  display: block;
  opacity: 1;
}

#mod-manager-main.active {
  display: block;
  opacity: 1;
}

/* Removed problematic background overlay */

/* --- Remove all 3D tilt styles --- */
.steam-game-card {
  /* Remove perspective and tilt styles */
}
.steam-game-card .game-image-container,
.steam-game-card .game-info {
  /* Remove transform-style and will-change */
  transition: box-shadow 0.3s ease;
}
.steam-game-card.tilt-active .game-image-container,
.steam-game-card.tilt-active .game-info {
  /* Remove tilt-active shadow */
  box-shadow: none;
}

/* --- Fix mod-content visibility and transitions --- */
/* Removed duplicate mod-content block */

/* Smooth refresh animations for mod grid */
@keyframes refreshSlideOut {
  0% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
  100% {
    opacity: 0;
    transform: translateX(-20px) scale(0.98);
  }
}

@keyframes refreshSlideIn {
  0% {
    opacity: 0;
    transform: translateX(20px) scale(0.98);
  }
  100% {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.mod-grid.refresh-slide-out {
  animation: refreshSlideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.mod-grid.refresh-slide-in {
  animation: refreshSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Enhanced transitions for individual mod items during refresh */
.mod-grid.refresh-slide-in .mod-item {
  opacity: 0;
  transform: translateY(10px);
  animation: modItemSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.mod-grid.refresh-slide-in .mod-item:nth-child(1) { animation-delay: 0.05s; }
.mod-grid.refresh-slide-in .mod-item:nth-child(2) { animation-delay: 0.1s; }
.mod-grid.refresh-slide-in .mod-item:nth-child(3) { animation-delay: 0.15s; }
.mod-grid.refresh-slide-in .mod-item:nth-child(4) { animation-delay: 0.2s; }
.mod-grid.refresh-slide-in .mod-item:nth-child(5) { animation-delay: 0.25s; }
.mod-grid.refresh-slide-in .mod-item:nth-child(n+6) { animation-delay: 0.3s; }

@keyframes modItemSlideIn {
  0% {
    opacity: 0;
    transform: translateY(15px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Refresh loading state improvements */
.mod-grid .loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: var(--bg-secondary);
  border-radius: 12px;
  margin: 20px 0;
  border: 1px solid var(--border-color);
}

.mod-grid .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

.mod-grid .loading-message p {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
}

/* Beta Warning Banner */
.beta-warning-banner {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    border: 1px solid #d97706;
    border-radius: 10px;
    padding: 16px 20px;
    margin: 16px 0 24px 0;
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 4px 12px rgba(251, 191, 36, 0.15);
    animation: warningGlow 2s ease-in-out infinite alternate;
}

.beta-warning-banner .warning-icon {
    font-size: 24px;
    animation: warningPulse 2s ease-in-out infinite;
}

.beta-warning-banner .warning-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.beta-warning-banner .warning-content strong {
    color: #1f2937;
    font-size: 16px;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.beta-warning-banner .warning-content span {
    color: #374151;
    font-size: 14px;
    line-height: 1.5;
    font-weight: 500;
}

@keyframes warningGlow {
    0% { box-shadow: 0 4px 12px rgba(251, 191, 36, 0.15); }
    100% { box-shadow: 0 4px 20px rgba(251, 191, 36, 0.25); }
}

@keyframes warningPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Responsive design for the beta warning */
@media (max-width: 768px) {
    .beta-warning-banner {
        padding: 12px 16px;
        margin: 12px 0 20px 0;
        flex-direction: column;
        text-align: center;
        gap: 12px;
    }
    
    .beta-warning-banner .warning-icon {
        font-size: 20px;
    }
    
    .beta-warning-banner .warning-content strong {
        font-size: 15px;
    }
    
    .beta-warning-banner .warning-content span {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .beta-warning-banner {
        padding: 10px 12px;
        margin: 10px 0 16px 0;
    }
    
    .beta-warning-banner .warning-content strong {
        font-size: 14px;
    }
    
    .beta-warning-banner .warning-content span {
        font-size: 12px;
    }
}

.mod-manager-new-layout {
}

/* HWID Spoofer Styles */
.hwid-spoofer-panel {
    border: 1px solid var(--primary-color);
    position: relative;
    overflow: hidden;
}

.hwid-spoofer-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(0, 180, 255, 0.1), 
        transparent
    );
    animation: shimmer 3s infinite;
    z-index: 0;
}

.hwid-spoofer-panel > * {
    position: relative;
    z-index: 1;
}

.hwid-status-info {
    margin: 1rem 0;
    padding: 0.75rem;
    background: var(--background-hover);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
}

.status-item:not(:last-child) {
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 0.25rem;
    padding-bottom: 0.5rem;
}

.status-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.status-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.status-value.status-success {
    color: var(--success-color);
}

.status-value.status-warning {
    color: var(--warning-color);
}

.status-value.status-info {
    color: var(--primary-color);
}

.hwid-spoofing-options {
    margin: 1rem 0;
    padding: 0.75rem;
    background: rgba(0, 180, 255, 0.05);
    border-radius: 8px;
    border: 1px solid rgba(0, 180, 255, 0.2);
}

.hwid-spoofing-options .checkbox-label {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-primary);
}

.tool-advanced {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--background-hover);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    animation: slideInFromTop 0.3s ease-out;
}

.advanced-header {
    margin-bottom: 0.75rem;
}

.advanced-header h4 {
    font-size: 1rem;
    color: var(--text-primary);
    margin: 0;
}

.advanced-actions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.advanced-actions .btn {
    font-size: 0.9rem;
    padding: 0.5rem 1rem;
}

.tool-warning {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(255, 170, 0, 0.1);
    border: 1px solid var(--warning-color);
    border-radius: 8px;
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.warning-icon {
    font-size: 1.2rem;
    color: var(--warning-color);
    flex-shrink: 0;
    margin-top: 0.1rem;
}

.warning-text {
    font-size: 0.85rem;
    line-height: 1.4;
    color: var(--text-secondary);
}

.warning-text strong {
    color: var(--warning-color);
    font-weight: 600;
}

/* HWID Info Modal Styles - More specific to override general modal styles */
.custom-modal-overlay .hwid-info-modal.custom-modal-container {
    max-width: 95vw !important;
    width: 95vw !important;
    min-width: 1200px !important;
    max-height: 90vh !important;
    height: 90vh !important;
}

.hwid-info-modal .hwid-info-content {
    padding: 0;
    font-size: 1.1rem;
}

/* HWID Modal Body - Increase content area */
.hwid-info-modal .custom-modal-body {
    max-height: 72vh !important;
    padding: 1.5rem;
    overflow-y: auto;
}

/* HWID Modal Footer - Compact button area */
.hwid-info-modal .custom-modal-footer {
    padding: 1rem 1.5rem !important;
    background: linear-gradient(135deg, #1e293b, #0f172a);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    min-height: auto;
}

.hwid-info-modal .custom-modal-footer .btn {
    min-width: 100px;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.hwid-info-modal .info-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
}

.hwid-info-modal .info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.hwid-info-modal .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.hwid-info-modal .info-label {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-secondary);
    min-width: 150px;
}

.hwid-info-modal .info-value {
    font-size: 1rem;
    font-weight: 500;
    color: var(--text-primary);
    word-break: break-all;
    text-align: right;
    max-width: 600px;
}

.info-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--background-hover);
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.info-section:last-child {
    margin-bottom: 0;
}

.info-section h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.info-grid {
    display: grid;
    gap: 0.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.info-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

.info-value.active {
    color: var(--success-color);
}

.info-value.inactive {
    color: var(--text-muted);
}

.info-value.success {
    color: var(--success-color);
}

.info-value.warning {
    color: var(--warning-color);
}

.privacy-note {
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-secondary);
}

.privacy-note p {
    margin-bottom: 0.75rem;
}

.privacy-note p:last-child {
    margin-bottom: 0;
}

.privacy-note strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* MAC Spoof Modal Styles */
.mac-spoof-modal .modal-content {
    max-width: 650px;
}

.mac-spoof-content {
    max-height: 450px;
    overflow-y: auto;
}

.warning-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(255, 170, 0, 0.1);
    border: 1px solid var(--warning-color);
    border-radius: 8px;
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.warning-section .warning-icon {
    font-size: 1.5rem;
    color: var(--warning-color);
    flex-shrink: 0;
}

.warning-section .warning-text h4 {
    margin-bottom: 0.5rem;
    color: var(--warning-color);
    font-size: 1.1rem;
}

.warning-section .warning-text p {
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
    color: var(--text-secondary);
}

.adapters-section {
    margin-bottom: 1.5rem;
}

.adapters-section h4 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.1rem;
    font-weight: 600;
}

.adapters-list {
    background: var(--background-hover);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.adapter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
}

.adapter-item:last-child {
    border-bottom: none;
}

.adapter-name {
    font-size: 0.9rem;
    color: var(--text-primary);
    font-weight: 500;
    flex: 1;
    margin-right: 1rem;
}

.adapter-mac {
    font-size: 0.85rem;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
    background: var(--background-card);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

/* HWID Comparison Grid */
.hwid-comparison-grid {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.hwid-comparison-item {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 16px;
    transition: all 0.2s ease;
}

.hwid-comparison-item.modified {
    border-color: rgba(251, 191, 36, 0.3);
    background: rgba(251, 191, 36, 0.05);
}

.hwid-comparison-item.original {
    border-color: rgba(74, 222, 128, 0.3);
    background: rgba(74, 222, 128, 0.05);
}

.hwid-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.hwid-name {
    color: var(--text-primary);
    font-size: 15px;
    font-weight: 600;
}

.hwid-status {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
}

.hwid-status.status-modified {
    color: var(--warning-color);
    background: rgba(251, 191, 36, 0.2);
}

.hwid-status.status-original {
    color: var(--success-color);
    background: rgba(74, 222, 128, 0.2);
}

.hwid-values {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.hwid-value-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 8px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.hwid-value-row:last-child {
    border-bottom: none;
}

.value-type {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
    min-width: 80px;
}

.value-data {
    color: var(--text-primary);
    font-size: 13px;
    font-family: 'Courier New', monospace;
    word-break: break-all;
    text-align: right;
    max-width: 400px;
}

.value-data.modified-value {
    color: var(--warning-color);
}

.value-data.original-value {
    color: var(--success-color);
}

.comparison-header {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
}

.comparison-header p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.comparison-header strong {
    color: var(--text-primary);
}

.error-message {
    padding: 1rem;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid rgba(255, 0, 0, 0.3);
    border-radius: 6px;
    text-align: center;
}

.error-message p {
    margin: 0;
    color: #ff6b6b;
    font-weight: 500;
}

.info-value.modified {
    color: var(--warning-color);
}

.info-value.original {
    color: var(--success-color);
}

.hwid-info-modal.custom-modal-container {
    max-width: 95vw;
    width: 95vw;
    max-height: 90vh;
    overflow-y: auto;
}

/* Kernel Protection Section */
.kernel-protection-section {
    margin: 1.5rem 0;
    padding: 1.25rem;
    background: linear-gradient(135deg, rgba(0, 180, 255, 0.05), rgba(0, 100, 200, 0.05));
    border: 1px solid rgba(0, 180, 255, 0.3);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
}

.kernel-protection-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(0, 180, 255, 0.1), 
        transparent
    );
    animation: shimmer 4s infinite;
    z-index: 0;
}

.kernel-protection-section > * {
    position: relative;
    z-index: 1;
}

.kernel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.kernel-header h5 {
    margin: 0;
    color: #00b4ff;
    font-size: 1.1rem;
    font-weight: 700;
    text-shadow: 0 0 10px rgba(0, 180, 255, 0.3);
}

.kernel-status {
    font-size: 0.85rem;
    font-weight: 600;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-secondary);
}

.kernel-status.available {
    background: rgba(0, 255, 0, 0.2);
    border-color: rgba(0, 255, 0, 0.4);
    color: #00ff00;
}

.kernel-status.unavailable {
    background: rgba(255, 0, 0, 0.2);
    border-color: rgba(255, 0, 0, 0.4);
    color: #ff4444;
}

.kernel-status.active {
    background: rgba(0, 180, 255, 0.3);
    border-color: rgba(0, 180, 255, 0.5);
    color: #00b4ff;
    animation: kernelGlow 2s ease-in-out infinite alternate;
}

@keyframes kernelGlow {
    0% { 
        box-shadow: 0 0 5px rgba(0, 180, 255, 0.3);
        transform: scale(1);
    }
    100% { 
        box-shadow: 0 0 15px rgba(0, 180, 255, 0.6);
        transform: scale(1.02);
    }
}

.kernel-description {
    margin-bottom: 1rem;
}

.kernel-description p {
    margin: 0;
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.kernel-actions {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.btn-kernel {
    background: linear-gradient(135deg, #00b4ff, #0066cc);
    border: 1px solid #00b4ff;
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    box-shadow: 0 2px 8px rgba(0, 180, 255, 0.3);
    transition: all 0.3s ease;
}

.btn-kernel:hover:not(:disabled) {
    background: linear-gradient(135deg, #0099dd, #0055bb);
    box-shadow: 0 4px 15px rgba(0, 180, 255, 0.5);
    transform: translateY(-1px);
}

.btn-kernel:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 180, 255, 0.3);
}

.btn-kernel:disabled {
    background: linear-gradient(135deg, #666, #555);
    border-color: #666;
    color: #999;
    cursor: not-allowed;
    box-shadow: none;
}

.btn-kernel.active {
    background: linear-gradient(135deg, #ff6b35, #cc5529);
    border-color: #ff6b35;
    animation: kernelActive 2s ease-in-out infinite alternate;
}

@keyframes kernelActive {
    0% { 
        box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
    }
    100% { 
        box-shadow: 0 4px 15px rgba(255, 107, 53, 0.6);
    }
}

.kernel-warning {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 0.75rem;
    background: rgba(255, 170, 0, 0.1);
    border: 1px solid rgba(255, 170, 0, 0.3);
    border-radius: 8px;
}

.kernel-warning .warning-icon {
    font-size: 1.1rem;
    color: #ffaa00;
    flex-shrink: 0;
    margin-top: 0.1rem;
}

.kernel-warning .warning-text {
    font-size: 0.85rem;
    line-height: 1.4;
    color: var(--text-secondary);
}

.kernel-warning .warning-text strong {
    color: #ffaa00;
    font-weight: 600;
}

@media (max-width: 768px) {
    .hwid-status-info {
        padding: 0.5rem;
    }
    
    .status-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .tool-advanced {
        padding: 0.75rem;
    }
    
    .advanced-actions {
        gap: 0.4rem;
    }
    
    .tool-warning {
        padding: 0.5rem;
        gap: 0.5rem;
    }
    
    .warning-text {
        font-size: 0.8rem;
    }
    
    .info-section {
        padding: 0.75rem;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .warning-section {
        padding: 0.75rem;
        gap: 0.75rem;
    }
    
    .adapter-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        padding: 0.75rem;
    }
    
    .adapter-name {
        margin-right: 0;
    }
    
    .hwid-comparison-grid {
        gap: 12px;
    }
    
    .hwid-comparison-item {
        padding: 12px;
    }
    
    .hwid-label {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
        margin-bottom: 8px;
    }
    
    .hwid-value-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        padding: 6px 0;
    }
    
    .value-data {
        max-width: 100%;
        text-align: left;
        font-size: 12px;
    }
    
    .hwid-info-modal.custom-modal-container {
        max-width: 98vw;
        width: 98vw;
        max-height: 85vh;
    }
    
    .kernel-protection-section {
        margin: 1rem 0;
        padding: 1rem;
    }
    
    .kernel-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    .kernel-header h5 {
        font-size: 1rem;
    }
    
    .kernel-actions {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn-kernel {
        width: 100%;
        padding: 0.75rem;
        font-size: 0.9rem;
    }
    
    .kernel-warning {
        padding: 0.5rem;
        gap: 0.5rem;
    }
    
    .kernel-warning .warning-text {
        font-size: 0.8rem;
    }
}

/* Emergency Restore Modal */
.emergency-restore-modal .modal-content {
    max-width: 900px;
    max-height: 85vh;
    overflow-y: auto;
}

.emergency-restore-content {
    padding: 0;
}

.emergency-values {
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.emergency-values h5 {
    margin: 0 0 1rem 0;
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 600;
}

.value-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
.value-item {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.value-item strong {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 600;
}

.value-item code {
    display: block;
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    color: #4ade80;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    word-break: break-all;
    user-select: all;
}

.emergency-instructions {
    margin: 1.5rem 0;
    padding: 1rem;
    background: rgba(0, 180, 255, 0.05);
    border: 1px solid rgba(0, 180, 255, 0.2);
    border-radius: 8px;
}

.emergency-instructions h5 {
    margin: 0 0 1rem 0;
    color: #000b4fff;
    font-size: 1rem;
    font-weight: 600;
}

.emergency-instructions ol {
    margin: 0;
    padding-left: 1.5rem;
    color: var(--text-secondary);
}

.emergency-instructions li {
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.emergency-note {
    margin: 1.5rem 0 0 0;
    padding: 1rem;
    background: rgba(255, 170, 0, 0.05);
    border: 1px solid rgba(255, 170, 0, 0.2);
    border-radius: 8px;
}

.emergency-note p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
}

.emergency-note strong {
    color: #ffaa00;
}

.status-info {
    color: #000b4fff;
}

/* Account Tab Styles */
.account-container {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.account-card {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 2rem;
    margin-bottom: 2rem;
}

.overview-card {
    background: linear-gradient(135deg, var(--background-card) 0%, rgba(0, 180, 255, 0.05) 100%);
}

.account-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.account-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    box-shadow: 0 4px 20px rgba(0, 180, 255, 0.3);
}

.account-info {
    flex: 1;
}

.account-username {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.account-email {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.account-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 20px;
    color: var(--success-color);
    font-size: 0.875rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.account-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.account-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 2rem;
}

.account-section {
    background: var(--background-card);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

.account-section h3 {
    margin-bottom: 1.5rem;
}

.license-card {
    background: var(--background-hover);
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

.license-info {
    margin-bottom: 1rem;
}

.license-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.license-key-display {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    background: var(--background-dark);
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.license-key-display .btn-icon {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.25rem;
    transition: var(--transition-smooth);
}

.license-key-display .btn-icon:hover {
    color: var(--primary-color);
}

.license-actions {
    display: flex;
    gap: 1rem;
}

.security-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.security-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: var(--background-hover);
    border-radius: var(--border-radius);
}

.security-info h4 {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.security-info p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.premium-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--background-hover);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    transition: var(--transition-smooth);
}

.feature-item.active {
    border-color: var(--success-color);
    background: rgba(0, 255, 136, 0.05);
}

.feature-icon {
    font-size: 1.5rem;
}

.feature-name {
    flex: 1;
    font-weight: 500;
}

.feature-status {
    font-size: 1.2rem;
}

.upgrade-cta {
    text-align: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 180, 255, 0.1) 0%, rgba(0, 255, 136, 0.1) 100%);
    border-radius: var(--border-radius);
    border: 1px solid var(--primary-color);
}

.upgrade-cta p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

.data-options {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn-danger {
    background: var(--error-color);
    color: white;
}

.btn-danger:hover {
    background: #dc2626;
    transform: translateY(-2px);
}

/* ===== ADD MODS MODAL STYLING ===== */
/* Moved from inline to prevent beta warning interference */

/* Add Mods Modal - Isolated styling that won't affect other elements */
.add-mods-modal-wide {
    max-width: 700px !important;
    width: 85vw !important;
    min-width: 500px !important;
    max-height: 80vh !important;
}

.add-mods-modal-wide .custom-modal-body {
    max-height: 60vh !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

/* Modal container animation without affecting document flow */
.add-mods-modal-wide {
    transform: scale(0.8) !important;
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
}

.custom-modal-overlay.active .add-mods-modal-wide {
    transform: scale(1) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .add-mods-modal-wide {
        width: 95vw !important;
        min-width: 300px !important;
        max-width: none !important;
    }
    
    .format-tags-container {
        justify-content: flex-start !important;
    }
    
    .step-list {
        gap: 0.5rem !important;
    }
    
    .step-text {
        font-size: 0.85rem !important;
    }
}

/* Upload Instructions Styling */
.upload-instructions {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(29, 78, 216, 0.05) 100%);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.instructions-icon {
    font-size: 2rem;
    margin-bottom: 0.75rem;
}

.upload-instructions h4 {
    color: #f9fafb;
    margin: 0 0 1rem 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.step-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 1rem;
    text-align: left;
}

.step {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.03);
    border-radius: 8px;
}

.step-number {
    background: #3b82f6;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.85rem;
    font-weight: 600;
    flex-shrink: 0;
}

.step-text {
    color: #e2e8f0;
    font-size: 0.9rem;
    line-height: 1.4;
}

.supported-formats-info {
    color: #9ca3af;
    font-size: 0.85rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.supported-formats-info strong {
    color: #06b6d4;
    font-weight: 600;
    display: block;
    margin-bottom: 0.5rem;
}

.format-tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
}

.format-tag {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    color: #06b6d4;
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid rgba(6, 182, 212, 0.3);
    transition: all 0.2s ease;
}

.format-tag:hover {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(6, 182, 212, 0.3);
}

/* File Browse Section */
.file-browse-section {
    text-align: center;
    margin-bottom: 1.5rem;
}

.btn-browse-files {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border: none;
    color: white;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-browse-files:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.browse-icon {
    font-size: 1.1rem;
}

.browse-help {
    color: #06b6d4;
    font-size: 0.9rem;
    margin: 0.75rem 0 0 0;
    font-weight: 500;
    background: rgba(6, 182, 212, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    border: 1px solid rgba(6, 182, 212, 0.2);
    display: inline-block;
}

.file-item-professional {
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
    border: 1px solid #4b5563;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    align-items: center;
    gap: 16px;
}

.file-item-professional:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.file-details {
    display: flex;
    flex-direction: column;
    gap: 4px;
    min-width: 0; /* Allow text truncation */
}

.file-name {
    font-weight: 600;
    color: #f9fafb;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    color: #9ca3af;
    font-size: 12px;
}

.file-category-wrapper {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 200px;
}

.category-label {
    color: #06b6d4;
    font-weight: 600;
    font-size: 13px;
    white-space: nowrap;
}

.file-category-select {
    flex: 1;
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border: 1px solid #374151;
    border-radius: 8px;
    color: #f9fafb;
    padding: 8px 10px;
    font-size: 13px;
    transition: all 0.3s ease;
    min-width: 140px;
    appearance: none;
    cursor: pointer;
}

.file-category-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.file-category-select:hover {
    border-color: #4b5563;
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

.file-category-select option {
    background: #1f2937;
    color: #f9fafb;
    padding: 8px;
    border: none;
}

.file-category-select option:hover,
.file-category-select option:checked {
    background: #3b82f6;
    color: white;
}

/* Highlight files missing categories */
.file-item-professional.missing-category {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
    animation: shake 0.5s ease-in-out;
}

.file-item-professional.missing-category .file-category-select {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Warning popup styling */
.category-warning-popup {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
    z-index: 1000;
    margin-bottom: 8px;
    white-space: nowrap;
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.category-warning-popup.show {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.category-warning-popup::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: #dc2626;
}

/* Button shake animation */
.btn-shake {
    animation: buttonShake 0.5s ease-in-out;
}

@keyframes buttonShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
    20%, 40%, 60%, 80% { transform: translateX(3px); }
}

/* Bulk category selection styling */
.bulk-category-section {
    background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
    border: 1px solid #334155;
    border-radius: 10px;
    padding: 16px;
    margin-bottom: 16px;
}

.bulk-category-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.bulk-category-label {
    color: #06b6d4;
    font-weight: 600;
    font-size: 14px;
    white-space: nowrap;
}

.bulk-category-select {
    background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
    border: 1px solid #374151;
    border-radius: 8px;
    color: #f9fafb;
    padding: 8px 12px;
    font-size: 13px;
    transition: all 0.3s ease;
    min-width: 200px;
    cursor: pointer;
    appearance: none;
}

.bulk-category-select:focus {
    outline: none;
    border-color: #06b6d4;
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
}

.bulk-category-select:hover {
    border-color: #4b5563;
    background: linear-gradient(135deg, #374151 0%, #1f2937 100%);
}

.bulk-category-select option {
    background: #1f2937;
    color: #f9fafb;
    padding: 8px;
    border: none;
}

.bulk-category-select option:hover,
.bulk-category-select option:checked {
    background: #3b82f6;
    color: white;
}

.bulk-category-help {
    color: #9ca3af;
    font-size: 12px;
    margin: 0;
    font-style: italic;
}

.btn-remove-file {
    background: #ef4444;
    color: white;
    border: none;
    border-radius: 6px;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
    font-weight: bold;
}

.btn-remove-file:hover {
    background: #dc2626;
    transform: scale(1.05);
}

.custom-modal .warning-content {
    text-align: center;
    padding: 20px;
}

.custom-modal .warning-icon-large {
    font-size: 48px;
    margin-bottom: 16px;
}

.custom-modal .warning-content h3 {
    color: #f59e0b;
    margin-bottom: 12px;
    font-size: 18px;
}

.missing-files-list {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    color: #fecaca;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.6;
}

.warning-note {
    color: #9ca3af;
    font-size: 14px;
    margin-top: 16px;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 12px;
}

/* === Download Progress Animations === */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Download Progress Container Global Styles */
.download-progress-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    width: 350px;
    pointer-events: none;
}

.download-progress-item {
    background: linear-gradient(135deg, #1f2937, #111827);
    border: 2px solid rgba(59, 130, 246, 0.5);
    border-radius: 12px;
    padding: 1.2rem;
    margin-bottom: 0.8rem;
    color: #f9fafb;
    font-size: 1rem;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(15px);
    pointer-events: auto;
    animation: slideInRight 0.4s ease, pulse 2s ease-in-out infinite;
    z-index: 10001;
}

@keyframes pulse {
    0%, 100% { 
        border-color: rgba(59, 130, 246, 0.5);
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.6);
    }
    50% { 
        border-color: rgba(59, 130, 246, 0.8);
        box-shadow: 0 12px 40px rgba(59, 130, 246, 0.3);
    }
}

/* Enhanced Modal Styles for Download Dialogs */
/* Enhanced Download Mods Modal - Isolated styling that won't affect other elements */
.enhanced-download-mods-modal {
    max-width: 650px !important;
    width: 90vw !important;
    min-width: 500px !important;
    max-height: 80vh !important;
}

.enhanced-download-mods-modal .custom-modal-body {
    max-height: 60vh !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

/* Modal container animation without affecting document flow */
.enhanced-download-mods-modal {
    transform: scale(0.8) !important;
    transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
}

.custom-modal-overlay.active .enhanced-download-mods-modal {
    transform: scale(1) !important;
}

.enhanced-download-mods-modal .download-mods-container {
    max-height: none !important;
    overflow: visible !important;
    padding-right: 0 !important;
}

.download-categorization-modal .modal-content {
    max-width: 600px;
}

.browser-loading-modal .modal-content {
    max-width: 550px;
    text-align: center;
}

/* Download categorization modal doesn't close on outside click */
.download-categorization-modal .modal-overlay {
    pointer-events: auto;
}

/* Browser loading modal doesn't close on outside click */
.browser-loading-modal .modal-overlay {
    pointer-events: none;
}

.browser-loading-modal .modal-content {
    pointer-events: auto;
}



/* === End of Download Progress Animations === */
