/* ===================================
   Responsive Design
   =================================== */

/* Large Desktop (1200px and up) */
@media (min-width: 1200px) {
    .modern-dashboard {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .main-tools-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Medium Desktop (992px to 1199px) */
@media (max-width: 1199px) and (min-width: 992px) {
    .modern-dashboard {
        grid-template-columns: 1fr;
    }
    
    .main-tools-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Tablet and Small Desktop (768px to 1199px) */
@media (max-width: 1200px) {
    .modern-dashboard {
        grid-template-columns: 1fr;
    }
    
    .quick-access-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
    
    .launch-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
    
    .tools-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .game-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

/* Tablet Portrait (768px and below) */
@media (max-width: 768px) {
    /* Layout Adjustments */
    .modern-dashboard {
        gap: 1.5rem;
    }
    
    .dashboard-section {
        padding: 1.5rem;
    }
    
    .main-content {
        padding: 1.5rem;
    }
    
    /* Header Adjustments */
    .app-header {
        padding: 0.5rem 1rem;
        height: 50px;
    }
    
    .app-title {
        font-size: 1.1rem;
    }
    
    .current-tab-indicator {
        font-size: 1rem;
        padding: 0.4rem 0.8rem;
    }
    
    /* Welcome Section */
    .welcome-clock-header {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
        padding: 1.25rem;
    }

    .clock-calendar-compact {
        flex-direction: column;
        gap: 1rem;
    }

    .clock-display {
        text-align: center;
    }

    .mini-calendar-compact {
        width: 100%;
        max-width: 280px;
        margin: 0 auto;
    }

    .welcome-message {
        font-size: 1.4rem;
    }

    .time-compact {
        font-size: 1.6rem;
    }
    
    /* Grid Adjustments */
    .launch-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.75rem;
    }
    
    .launch-app {
        padding: 1rem 0.75rem;
    }
    
    .app-icon {
        width: 40px;
        height: 40px;
        font-size: 1.6rem;
    }
    
    .quick-access-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.75rem;
    }
    
    .favorite-icon {
        width: 40px;
        height: 40px;
        font-size: 1.6rem;
    }
    
    .tools-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }
    
    .tool-card {
        padding: 1.5rem;
    }
    
    .tool-icon {
        font-size: 2.5rem;
    }
    
    .game-grid {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 0.75rem;
    }
    
    .game-card {
        padding: 0.75rem;
    }
    
    .game-card img {
        height: 100px;
    }
    
    /* Section Headers */
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .section-header h3 {
        font-size: 1.2rem;
    }
    
    /* Modal Adjustments */
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-xlarge {
        width: 98%;
        height: 90vh;
    }
    
    .modal-header {
        padding: 1rem;
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 1rem;
        flex-direction: column;
        gap: 0.5rem;
    }
    
    /* File Browser */
    .file-browser-container {
        height: 60vh;
    }
    
    .file-sidebar {
        width: 180px;
        padding: 0.75rem;
    }
    
    .file-list {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.5rem;
    }
    
    .file-item {
        padding: 0.75rem 0.5rem;
    }
    
    .file-item-icon {
        font-size: 1.5rem;
    }
    
    .file-item-name {
        font-size: 0.75rem;
    }
    
    /* Tool Panels */
    .main-tools-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .tool-panel {
        padding: 1rem;
    }
    
    .config-group {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .config-row.inline {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .config-row.inline select {
        max-width: none;
        width: 100%;
    }
    
    /* Action Buttons */
    .action-buttons {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .btn {
        min-width: auto;
        width: 100%;
    }
}

/* Mobile Landscape (576px to 767px) */
@media (max-width: 767px) and (min-width: 576px) {
    .welcome-clock-header {
        flex-direction: row;
        text-align: left;
    }
    
    .clock-calendar-compact {
        flex-direction: row;
        gap: 1.5rem;
    }
    
    .mini-calendar-compact {
        max-width: 200px;
    }
}

/* Mobile Portrait (575px and below) */
@media (max-width: 575px) {
    /* Ultra-compact layout */
    .main-content {
        padding: 1rem;
    }
    
    .dashboard-section {
        padding: 1rem;
    }
    
    .welcome-clock-header {
        padding: 1rem;
    }
    
    .welcome-message {
        font-size: 1.2rem;
    }
    
    .time-compact {
        font-size: 1.4rem;
    }
    
    .launch-grid,
    .quick-access-grid {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 0.5rem;
    }
    
    .launch-app,
    .favorite-card {
        padding: 0.75rem 0.5rem;
    }
    
    .app-icon,
    .favorite-icon {
        width: 32px;
        height: 32px;
        font-size: 1.2rem;
    }
    
    .app-name,
    .favorite-name {
        font-size: 0.8rem;
    }
    
    .tools-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .tool-card {
        padding: 1rem;
        text-align: left;
    }
    
    .tool-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }
    
    .game-grid {
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    }
    
    .section-header h3 {
        font-size: 1.1rem;
    }
    
    /* Sidebar adjustments */
    .sidebar {
        width: 240px;
    }
    
    .nav-item {
        padding: 12px 10px;
        font-size: 0.9rem;
    }
    
    .nav-item .icon {
        font-size: 1.1rem;
        width: 20px;
    }
    
    /* Modal ultra-compact */
    .modal-content {
        width: 98%;
        margin: 0.5rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 0.75rem;
    }
    
    .file-browser-container {
        height: 50vh;
    }
    
    .file-sidebar {
        width: 150px;
        padding: 0.5rem;
    }
    
    .sidebar-section h5 {
        font-size: 0.7rem;
    }
    
    .sidebar-item {
        padding: 0.4rem 0.5rem;
        font-size: 0.8rem;
    }
}

/* Print Styles */
@media print {
    .bg-effects,
    .sidebar,
    .app-header,
    .modal-overlay,
    .control-btn,
    .menu-toggle {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        background: white;
        color: black;
    }
    
    .dashboard-section {
        background: white;
        border: 1px solid #ccc;
        box-shadow: none;
        page-break-inside: avoid;
        margin-bottom: 1rem;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #666666;
        --text-secondary: #cccccc;
        --background-hover: #333333;
    }
    
    .dashboard-section {
        border-width: 2px;
    }
    
    .btn {
        border: 2px solid currentColor;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .particles::before,
    .grid-overlay {
        animation: none;
    }
}

/* Manual Reduced Motion Class */
.reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}
