/* ===================================
   Animations and Visual Effects
   =================================== */

/* Background Effects */
.bg-effects {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.particles::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(0, 180, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 180, 255, 0.1) 0%, transparent 50%);
    animation: particleFloat 20s infinite linear;
}

.grid-overlay {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 180, 255, 0.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 180, 255, 0.05) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 30s infinite linear;
}

/* Keyframe Animations */
@keyframes particleFloat {
    0% { transform: translateY(0px) rotate(0deg); }
    100% { transform: translateY(-100px) rotate(360deg); }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

@keyframes gentleGlow {
    0%, 100% {
        text-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
    }
    50% {
        text-shadow: 0 0 25px rgba(255, 255, 255, 0.5), 0 0 35px rgba(0, 212, 255, 0.3);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideDown {
    from { 
        opacity: 0; 
        max-height: 0; 
        margin-top: 0;
    }
    to { 
        opacity: 1; 
        max-height: 100px;
        margin-top: 0.75rem;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes scaleOut {
    from {
        opacity: 1;
        transform: scale(1);
    }
    to {
        opacity: 0;
        transform: scale(0.8);
    }
}

/* Animation Utility Classes */
.animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
}

.animate-fadeOut {
    animation: fadeOut 0.3s ease-out;
}

.animate-slideInFromTop {
    animation: slideInFromTop 0.5s ease-out;
}

.animate-slideInFromRight {
    animation: slideInFromRight 0.5s ease-out;
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-shake {
    animation: shake 0.5s ease-in-out;
}

/* Hover Effects */
.hover-lift {
    transition: transform var(--transition-smooth);
}

.hover-lift:hover {
    transform: translateY(-2px);
}

.hover-glow {
    transition: box-shadow var(--transition-smooth);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(0, 180, 255, 0.3);
}

.hover-scale {
    transition: transform var(--transition-smooth);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 1.5s infinite;
}

/* Transition Utilities */
.transition-all {
    transition: all var(--transition-smooth);
}

.transition-fast {
    transition: all var(--transition-fast);
}

.transition-colors {
    transition: color var(--transition-smooth), background-color var(--transition-smooth), border-color var(--transition-smooth);
}

.transition-transform {
    transition: transform var(--transition-smooth);
}

.transition-opacity {
    transition: opacity var(--transition-smooth);
}

/* ===================================
   Advanced Animation Effects
   =================================== */

/* Shimmer Effects */
@keyframes shimmerGold {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
    }
    50% {
        box-shadow: 0 4px 16px rgba(255, 215, 0, 0.6);
    }
}

/* Steam Game Card Advanced Animations */
.steam-game-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -75%;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent);
    z-index: 2;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.steam-game-card:hover::before {
    animation: shimmer-move 1.2s cubic-bezier(0.4,0,0.2,1) 1;
    opacity: 1;
}

@keyframes shimmer-move {
    0% { left: -75%; opacity: 0; }
    10% { opacity: 1; }
    60% { left: 100%; opacity: 1; }
    100% { left: 100%; opacity: 0; }
}

/* Particle Burst Effect */
.steam-game-card.selected {
    box-shadow: 0 0 0 4px var(--primary-color), 0 0 40px 10px rgba(59,130,246,0.15);
    animation: card-pop 0.5s cubic-bezier(0.4,0,0.2,1);
}

@keyframes card-pop {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.particle-burst {
    position: absolute;
    pointer-events: none;
    left: 50%;
    top: 50%;
    width: 0;
    height: 0;
    z-index: 99;
}

.particle {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3b82f6, #06b6d4);
    opacity: 0.8;
    pointer-events: none;
    animation: particle-burst-anim 0.7s forwards;
}

@keyframes particle-burst-anim {
    0% { transform: scale(0.5) translate(0,0); opacity: 1; }
    80% { opacity: 1; }
    100% { transform: scale(1.2) translate(var(--dx), var(--dy)); opacity: 0; }
}



/* ===================================
   View Transition Animations
   =================================== */

/* Custom View Transition Animations (Mod Manager) */
@keyframes slideOutLeft {
    0% { opacity: 1; transform: translateX(0); }
    100% { opacity: 0; transform: translateX(-80px) scale(0.95); }
}

@keyframes slideInRight {
    0% { opacity: 0; transform: translateX(80px) scale(0.95); }
    100% { opacity: 1; transform: translateX(0); }
}

@keyframes slideOutRight {
    0% { opacity: 1; transform: translateX(0); }
    100% { opacity: 0; transform: translateX(80px) scale(0.95); }
}

@keyframes slideInLeft {
    0% { opacity: 0; transform: translateX(-80px) scale(0.95); }
    100% { opacity: 1; transform: translateX(0); }
}

/* View Transition Utility Classes */
.slide-out-left {
    animation: slideOutLeft 0.45s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
}

.slide-in-right {
    animation: slideInRight 0.45s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
}

.slide-out-right {
    animation: slideOutRight 0.45s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
}

.slide-in-left {
    animation: slideInLeft 0.45s cubic-bezier(0.4, 0.0, 0.2, 1) forwards;
}

.fade-in-fast {
    animation: fadeIn 0.2s ease-out;
}

/* ===================================
   Mod Grid Refresh Animations
   =================================== */

/* Smooth refresh animations for mod grid */
@keyframes refreshSlideOut {
    0% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateX(-20px) scale(0.98);
    }
}

@keyframes refreshSlideIn {
    0% {
        opacity: 0;
        transform: translateX(20px) scale(0.98);
    }
    100% {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.mod-grid.refresh-slide-out {
    animation: refreshSlideOut 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.mod-grid.refresh-slide-in {
    animation: refreshSlideIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

/* Enhanced transitions for individual mod items during refresh */
.mod-grid.refresh-slide-in .mod-item {
    opacity: 0;
    transform: translateY(10px);
    animation: modItemSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.mod-grid.refresh-slide-in .mod-item:nth-child(1) { animation-delay: 0.05s; }
.mod-grid.refresh-slide-in .mod-item:nth-child(2) { animation-delay: 0.1s; }
.mod-grid.refresh-slide-in .mod-item:nth-child(3) { animation-delay: 0.15s; }
.mod-grid.refresh-slide-in .mod-item:nth-child(4) { animation-delay: 0.2s; }
.mod-grid.refresh-slide-in .mod-item:nth-child(5) { animation-delay: 0.25s; }
.mod-grid.refresh-slide-in .mod-item:nth-child(n+6) { animation-delay: 0.3s; }

@keyframes modItemSlideIn {
    0% {
        opacity: 0;
        transform: translateY(15px) scale(0.95);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Tab Slide Animation */
@keyframes tabSlideIn {
    from {
        transform: scaleX(0);
    }
    to {
        transform: scaleX(1);
    }
}

/* Modal Slide Animation */
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Responsive adjustments for advanced animations */
@media (max-width: 768px) {
    .particles::before {
        animation-duration: 15s;
    }

    .particle {
        width: 6px;
        height: 6px;
    }

    .steam-game-card:hover::before {
        animation-duration: 0.8s;
    }

    .slide-out-left,
    .slide-in-right,
    .slide-out-right,
    .slide-in-left {
        animation-duration: 0.3s;
    }

    .mod-grid.refresh-slide-in .mod-item {
        animation-duration: 0.3s;
    }
}
